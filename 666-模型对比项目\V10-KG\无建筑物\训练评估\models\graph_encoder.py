import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl.function as fn
from compGCN import CompGraphConv

class GraphEncoder(nn.Module):
    """图编码器基类，可被GraphCL和BGRL使用"""
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list):
        super(GraphEncoder, self).__init__()
        self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        self.rel_embeds = nn.Embedding.from_pretrained(rel_emb, freeze=True)
        
        # 图卷积层
        self.layers = nn.ModuleList()
        self.layers.append(CompGraphConv(node_emb.shape[1], layer_sizes[0]))
        for i in range(len(layer_sizes)-1):
            self.layers.append(CompGraphConv(layer_sizes[i], layer_sizes[i+1]))
        
        # Dropout层
        self.dropouts = nn.ModuleList()
        for drop_rate in dropout_list:
            self.dropouts.append(nn.Dropout(drop_rate))
    
    def forward(self, g, node_idx=None):
        """图编码器前向传播"""
        h = self.node_embeds.weight
        r = self.rel_embeds.weight
        
        # 通过图卷积层
        for i, (layer, dropout) in enumerate(zip(self.layers, self.dropouts)):
            h, r = layer(g, h, r)
            h = dropout(h)
        
        # 返回所有节点的嵌入或特定节点的嵌入
        if node_idx is not None:
            return h[node_idx]
        else:
            return h 