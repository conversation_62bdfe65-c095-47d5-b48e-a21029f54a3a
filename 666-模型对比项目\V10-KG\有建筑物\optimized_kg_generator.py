"""
优化版空间多层次知识图谱生成器
主要优化：
1. 建筑物物理属性采用分类方式（面积：大中小，高度：高中低，年代：新中老）
2. 21种关系类型，层次清晰，连通性强
3. 三层空间嵌套：建筑物→地块→街区

关系类型（21种）：
A. 层次归属关系(3种): belongsToLand, belongsToRegion, locateAt
B. 属性关联关系(6种): hasFunction, hasMorphology, hasLandUse, hasDominantFunction, cateOf, hasPhysicalAttribute
C. 同层空间关系(6种): connectedTo, adjacentTo, borderBy, nearBy, similarMorphology, similarFunction
D. 跨层功能关系(4种): functionalSimilarity, highConvenience, functionalComplementarity, densityInfluence
E. 移动性关系(1种): flowTransition
F. 服务关系(1种): provideService
"""

import os
import sys
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from scipy.spatial.distance import cosine
import itertools
from tqdm import tqdm
import time
from collections import defaultdict, Counter

# ==================== 配置部分 ====================

# 数据路径配置
DATA_PATHS = {
    "l4_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L4.shp",
    "l5_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L5.shp",
    "poi_path": r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
    "bc_path": r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
    "checkin_path": r"D:\研二\能耗估算\1-沈阳\1-数据\6-微博数据\weibo1.csv",
    "building_path": r"C:\Users\<USER>\Desktop\22\9-建筑物数据\沈阳建筑物三环.shp",
    "land_use_path": r"C:\Users\<USER>\Desktop\22\14-地块数据\沈阳市.shp",
}

# 输出路径配置
OUTPUT_BASE_PATH = r"D:\研二\能耗估算\666-模型对比项目\KG\V10-KG\有建筑物\OUT"
OUTPUT_PATHS = {
    "kg_optimized": os.path.join(OUTPUT_BASE_PATH, "kg_optimized_21_relations.txt"),
    "category_mapping": os.path.join(OUTPUT_BASE_PATH, "category_mapping.csv"),
    "relation_stats": os.path.join(OUTPUT_BASE_PATH, "relation_statistics.csv"),
    "physical_attr_stats": os.path.join(OUTPUT_BASE_PATH, "physical_attribute_stats.csv"),
    "connectivity_analysis": os.path.join(OUTPUT_BASE_PATH, "connectivity_analysis.csv"),
}

# 参数配置
PARAMS = {
    # === 空间关系参数 ===
    "nearby_distance": 1000,        # nearBy关系距离阈值(米)
    "border_buffer": 10,            # 边界缓冲距离(米)
    "building_connection_distance": 150,  # 建筑物连接距离
    "land_adjacency_buffer": 5,     # 地块邻接缓冲距离
    
    # === 相似性参数 ===
    "function_similarity_threshold": 0.35,   # 功能相似度阈值
    "morphology_similarity_threshold": 0.7,  # 形态相似度阈值
    "building_function_similarity_threshold": 0.6,  # 建筑物功能相似度阈值
    
    # === 便利性参数 ===
    "convenience_min_categories": 6,         # 便利性最少POI类别数
    "convenience_min_pois": 15,              # 便利性最少POI总数
    "convenience_essential_categories": [     # 必需的基本服务类别
        "餐饮服务", "购物服务", "生活服务", "交通设施服务"
    ],
    "convenience_score_threshold": 10,       # 便利性得分门槛
    "convenience_distance_threshold": 1200,  # 便利性街区间最大距离
    
    # === 移动性参数 ===
    "flow_time_threshold": 7200,             # 流动时间阈值(秒)
    "flow_distance_threshold": 1500,         # 流动距离阈值
    
    # === 密度影响参数 ===
    "density_influence_threshold": 1.5,      # 密度影响关系阈值
    "density_influence_distance": 2000,      # 密度影响最大距离(米)
    
    # === 建筑物物理属性分类阈值 ===
    "area_thresholds": {
        "small_max": 200,      # 小面积上限（平方米）
        "large_min": 1000,     # 大面积下限（平方米）
    },
    "height_thresholds": {
        "low_max": 18,         # 低层上限（米，约6层）
        "high_min": 54,        # 高层下限（米，约18层）
    },
    "age_thresholds": {
        "old_max": 1990,       # 老建筑上限（年份）
        "new_min": 2010,       # 新建筑下限（年份）
    },
    
    # === Spacematrix形态分类阈值 ===
    "fsi_thresholds": [0.3, 0.6, 1.2, 2.0],  # 容积率阈值
    "gsi_thresholds": [0.1, 0.25, 0.4, 0.6], # 覆盖率阈值
    "l_thresholds": [3, 6, 12, 20],           # 层数阈值
    "floor_height": 3.3,                      # 层高估计值（米）
    
    # === 坐标系统 ===
    "target_crs": "EPSG:3857",        # Web Mercator投影坐标系
    
    # === ID前缀 ===
    "region_prefix": "Region_",
    "land_prefix": "Land_",
    "building_prefix": "Building_",
    "poi_prefix": "POI_",
    "category_prefix": "Cate_",
    "bc_prefix": "BC_",
    "morphology_prefix": "Morph_",
    "function_prefix": "Func_",
    "landuse_prefix": "LandUse_",
    "physical_attr_prefix": "PhysicalAttr_",
    "region_func_prefix": "RegionFunc_",
}

# 字段名映射
FIELD_MAPPING = {
    "l4": {"id_field": "BlockID", "geometry_field": "geometry"},
    "l5": {"id_field": "LandID", "geometry_field": "geometry"},
    "poi": {"name_field": "name", "category_field": "main_cat", "geometry_field": "geometry"},
    "bc": {"id_field": "OBJECTID", "name_field": "Name_CHN", "geometry_field": "geometry"},
    "building": {"id_field": "OBJECTID", "function_field": "Function", "height_field": "Height", 
                "area_field": "Area", "age_field": "Age", "geometry_field": "geometry"},
    "land_use": {"type_field": "Level2_cn", "geometry_field": "geometry"},
    "checkin": {"user_field": "账号", "time_field": "发布时间", "lon_field": "经度", "lat_field": "纬度"},
}

# 建筑物功能分类映射
BUILDING_FUNCTION_MAP = {
    "Residence": "Func_Residential",
    "Business": "Func_Commercial", 
    "Office": "Func_Office",
    "Industry": "Func_Industrial",
    "Public service": "Func_Public",
    "Education": "Func_Education",
    "Medical": "Func_Medical",
    "Cultural": "Func_Cultural",
    "Sports": "Func_Sports",
    "Transport": "Func_Transport",
    "Other": "Func_Other",
}

# 土地利用类型映射
LAND_USE_MAP = {
    "居住用地": "LandUse_Residential",
    "商业用地": "LandUse_Commercial",
    "工业用地": "LandUse_Industrial",
    "公共设施用地": "LandUse_Public",
    "绿地": "LandUse_Green",
    "交通用地": "LandUse_Transport",
    "水域": "LandUse_Water",
    "农业用地": "LandUse_Agricultural",
    "其他": "LandUse_Other",
}

# 形态类型
MORPHOLOGY_TYPES = [
    "Morph_LowRiseLowDensity", "Morph_LowRiseMidDensity", "Morph_LowRiseHighDensity",
    "Morph_MidRiseLowDensity", "Morph_MidRiseMidDensity", "Morph_MidRiseHighDensity", 
    "Morph_HighRiseLowDensity", "Morph_HighRiseMidDensity", "Morph_HighRiseHighDensity",
    "Morph_SuperHighRise", "Morph_Vacant",
]

# 流动模式配置
FLOW_PATTERNS = [
    ("交通设施服务", "商务住宅"), ("餐饮服务", "购物服务"), ("教育文化服务", "餐饮服务"),
    ("医疗保健服务", "生活服务"), ("体育休闲服务", "餐饮服务"), ("商务住宅", "生活服务"),
    ("办公", "餐饮服务"), ("住宅", "购物服务"),
]

# 高吸引力POI类型
HIGH_ATTRACTION_CATEGORIES = [
    "购物服务", "餐饮服务", "体育休闲服务", "旅游景点", 
    "教育文化服务", "交通设施服务", "医疗保健服务", "商务住宅"
]

# 功能互补关系配置
FUNCTIONAL_COMPLEMENTS = [
    ("餐饮服务", "购物服务"), ("生活服务", "体育休闲服务"), ("医疗保健服务", "生活服务"),
    ("教育文化服务", "体育休闲服务"), ("交通设施服务", "商务住宅"), ("办公", "餐饮服务"),
    ("住宅", "生活服务"), ("商业", "交通设施服务"),
]

# ==================== 工具函数 ====================

def ensure_dir(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        print(f"✅ 创建输出目录: {directory}")

def print_section(title):
    """打印带分隔线的标题"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80 + "\n")

def show_progress(iterable, desc, total=None):
    """进度条显示"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    return tqdm(iterable, desc=desc, total=total, ncols=100)

def ensure_projected_crs(gdf, target_crs='EPSG:3857'):
    """确保数据使用投影坐标系"""
    if gdf is None or gdf.empty:
        return gdf
    
    if gdf.crs and gdf.crs.is_geographic:
        gdf = gdf.to_crs(target_crs)
    elif gdf.crs and str(gdf.crs) != target_crs:
        gdf = gdf.to_crs(target_crs)
    elif not gdf.crs:
        gdf = gdf.set_crs('EPSG:4326').to_crs(target_crs)
    
    return gdf

def classify_physical_attribute(area, height, age):
    """
    建筑物物理属性分类
    返回组合分类ID，如：PhysicalAttr_LargeHighNew
    """
    # 面积分类
    if pd.isna(area) or area <= 0:
        area_class = "Medium"  # 默认值
    elif area < PARAMS["area_thresholds"]["small_max"]:
        area_class = "Small"
    elif area >= PARAMS["area_thresholds"]["large_min"]:
        area_class = "Large"
    else:
        area_class = "Medium"
    
    # 高度分类
    if pd.isna(height) or height <= 0:
        height_class = "Mid"  # 默认值
    elif height < PARAMS["height_thresholds"]["low_max"]:
        height_class = "Low"
    elif height >= PARAMS["height_thresholds"]["high_min"]:
        height_class = "High"
    else:
        height_class = "Mid"
    
    # 年代分类
    if pd.isna(age) or age <= 0:
        age_class = "Mid"  # 默认值
    elif age < PARAMS["age_thresholds"]["old_max"]:
        age_class = "Old"
    elif age >= PARAMS["age_thresholds"]["new_min"]:
        age_class = "New"
    else:
        age_class = "Mid"
    
    return f"{PARAMS['physical_attr_prefix']}{area_class}{height_class}{age_class}"

def classify_morphology_enhanced(FSI, GSI, OSR, L, max_height):
    """增强版形态分类"""
    if max_height > 100:
        return "Morph_SuperHighRise"
    
    if L < PARAMS["l_thresholds"][0]:  # 低层
        if GSI < PARAMS["gsi_thresholds"][0]:
            return "Morph_LowRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_LowRiseMidDensity"
        else:
            return "Morph_LowRiseHighDensity"
    elif L < PARAMS["l_thresholds"][1]:  # 中层
        if GSI < PARAMS["gsi_thresholds"][0]:
            return "Morph_MidRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_MidRiseMidDensity"
        else:
            return "Morph_MidRiseHighDensity"
    elif L < PARAMS["l_thresholds"][2]:  # 高层
        if GSI < PARAMS["gsi_thresholds"][1]:
            return "Morph_HighRiseLowDensity"
        elif GSI < PARAMS["gsi_thresholds"][2]:
            return "Morph_HighRiseMidDensity"
        else:
            return "Morph_HighRiseHighDensity"
    else:  # 超高层
        return "Morph_SuperHighRise"

# ==================== 数据加载与预处理 ====================

def load_all_data():
    """加载所有数据文件"""
    print_section("数据加载")
    
    data_files = [
        ("L4街区数据", DATA_PATHS["l4_shp_path"]),
        ("L5地块数据", DATA_PATHS["l5_shp_path"]),
        ("POI数据", DATA_PATHS["poi_path"]),
        ("商圈数据", DATA_PATHS["bc_path"]),
        ("签到数据", DATA_PATHS["checkin_path"]),
        ("建筑物数据", DATA_PATHS["building_path"]),
        ("土地利用数据", DATA_PATHS["land_use_path"]),
    ]
    
    results = []
    for desc, path in data_files:
        try:
            if path.endswith('.shp'):
                data = gpd.read_file(path)
            else:
                data = pd.read_csv(path)
            results.append(data)
            print(f"✅ {desc}: {len(data):,} 条记录")
        except Exception as e:
            print(f"❌ {desc} 加载失败: {e}")
            results.append(pd.DataFrame())
    
    return tuple(results)

def preprocess_all_data(l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf):
    """数据预处理"""
    print_section("数据预处理")
    
    # 1. 统一坐标系
    print("统一坐标系...")
    target_crs = PARAMS["target_crs"]
    l4_gdf = ensure_projected_crs(l4_gdf, target_crs)
    l5_gdf = ensure_projected_crs(l5_gdf, target_crs)
    poi_gdf = ensure_projected_crs(poi_gdf, target_crs)
    bc_gdf = ensure_projected_crs(bc_gdf, target_crs)
    building_gdf = ensure_projected_crs(building_gdf, target_crs)
    land_use_gdf = ensure_projected_crs(land_use_gdf, target_crs)
    
    # 2. 空间过滤
    print("根据L4边界过滤数据...")
    if not l4_gdf.empty:
        l4_boundary = l4_gdf.unary_union
        
        if not l5_gdf.empty:
            l5_gdf = l5_gdf[l5_gdf.intersects(l4_boundary)]
        if not poi_gdf.empty:
            poi_gdf = poi_gdf[poi_gdf.intersects(l4_boundary)]
        if not bc_gdf.empty:
            bc_gdf = bc_gdf[bc_gdf.intersects(l4_boundary)]
        if not building_gdf.empty:
            building_gdf = building_gdf[building_gdf.intersects(l4_boundary)]
    
    # 3. 添加统一ID
    print("生成统一ID...")
    
    # L4区域ID
    l4_gdf.columns = l4_gdf.columns.str.lower()
    id_field = FIELD_MAPPING["l4"]["id_field"].lower()
    if id_field in l4_gdf.columns:
        l4_gdf["region_id"] = l4_gdf[id_field].apply(lambda x: f"{PARAMS['region_prefix']}{x}")
    else:
        l4_gdf["region_id"] = l4_gdf.index.map(lambda x: f"{PARAMS['region_prefix']}{x}")
    
    # L5地块ID
    if not l5_gdf.empty:
        l5_gdf.columns = l5_gdf.columns.str.lower()
        if 'landid' in l5_gdf.columns:
            l5_gdf["land_id"] = l5_gdf['landid'].apply(lambda x: f"{PARAMS['land_prefix']}{x}")
        else:
            l5_gdf["land_id"] = l5_gdf.index.map(lambda x: f"{PARAMS['land_prefix']}{x}")
    
    # 建筑物ID和功能ID
    if not building_gdf.empty:
        building_gdf["building_id"] = building_gdf.index.map(lambda x: f"{PARAMS['building_prefix']}{x}")
        
        # 建筑功能
        function_field = FIELD_MAPPING["building"]["function_field"]
        if function_field in building_gdf.columns:
            building_gdf["function_id"] = building_gdf[function_field].map(
                lambda x: BUILDING_FUNCTION_MAP.get(str(x).strip(), "Func_Other") if pd.notna(x) else "Func_Other"
            )
        else:
            building_gdf["function_id"] = "Func_Other"
        
        # 建筑物理属性分类
        area_field = FIELD_MAPPING["building"].get("area_field", "Area")
        height_field = FIELD_MAPPING["building"]["height_field"]
        age_field = FIELD_MAPPING["building"].get("age_field", "Age")
        
        def get_physical_attr(row):
            area = row.get(area_field, np.nan)
            height = row.get(height_field, np.nan)
            age = row.get(age_field, np.nan)
            return classify_physical_attribute(area, height, age)
        
        building_gdf["physical_attr_id"] = building_gdf.apply(get_physical_attr, axis=1)
    
    # POI ID和类别ID
    if not poi_gdf.empty:
        poi_gdf["poi_id"] = poi_gdf.index.map(lambda x: f"{PARAMS['poi_prefix']}{x}")
        
        if 'main_cat' in poi_gdf.columns:
            unique_cats = poi_gdf['main_cat'].dropna().unique()
            category_mapping = {cat: f"{PARAMS['category_prefix']}{i}" for i, cat in enumerate(unique_cats)}
            poi_gdf["category_id"] = poi_gdf['main_cat'].map(
                lambda x: category_mapping.get(x, f"{PARAMS['category_prefix']}_None") if pd.notna(x) else f"{PARAMS['category_prefix']}_None"
            )
            
            # 保存类别映射
            ensure_dir(OUTPUT_PATHS["category_mapping"])
            pd.DataFrame({
                'main_cat': list(category_mapping.keys()),
                'category_id': list(category_mapping.values())
            }).to_csv(OUTPUT_PATHS["category_mapping"], index=False, encoding='utf-8')
    
    # 商圈ID
    if not bc_gdf.empty:
        if "OBJECTID" in bc_gdf.columns:
            bc_gdf["bc_id"] = bc_gdf["OBJECTID"].apply(lambda x: f"{PARAMS['bc_prefix']}{int(x)}" if pd.notna(x) else f"{PARAMS['bc_prefix']}0")
        else:
            bc_gdf["bc_id"] = bc_gdf.index.map(lambda x: f"{PARAMS['bc_prefix']}{x}")
    
    # 土地利用ID
    if not land_use_gdf.empty:
        land_use_gdf["landuse_id"] = land_use_gdf.index.map(lambda x: f"LandUse_{x}")
        type_field = FIELD_MAPPING["land_use"]["type_field"]
        if type_field in land_use_gdf.columns:
            land_use_gdf["landuse_type_id"] = land_use_gdf[type_field].map(
                lambda x: LAND_USE_MAP.get(str(x).strip(), "LandUse_Other") if pd.notna(x) else "LandUse_Other"
            )
        else:
            land_use_gdf["landuse_type_id"] = "LandUse_Other"
    
    # 4. 空间关联
    print("执行空间关联...")
    
    # L5地块与L4区域关联
    if not l5_gdf.empty:
        l5_gdf = gpd.sjoin(l5_gdf, l4_gdf[["geometry", "region_id"]], how='left', predicate='within')
        if "index_right" in l5_gdf.columns:
            l5_gdf.drop(columns=["index_right"], inplace=True)
    
    # 建筑物与L5地块关联
    if not building_gdf.empty and not l5_gdf.empty:
        building_gdf = gpd.sjoin(building_gdf, l5_gdf[["geometry", "land_id"]], how='left', predicate='within')
        if "index_right" in building_gdf.columns:
            building_gdf.drop(columns=["index_right"], inplace=True)
    
    # 建筑物与L4区域关联
    if not building_gdf.empty:
        building_gdf = gpd.sjoin(building_gdf, l4_gdf[["geometry", "region_id"]], how='left', predicate='within')
        if "index_right" in building_gdf.columns:
            building_gdf.drop(columns=["index_right"], inplace=True)
    
    # POI与区域关联
    if not poi_gdf.empty:
        poi_gdf = gpd.sjoin(poi_gdf, l4_gdf[["geometry", "region_id"]], how='left', predicate='within')
        if "index_right" in poi_gdf.columns:
            poi_gdf.drop(columns=["index_right"], inplace=True)
    
    # L5地块与土地利用关联
    if not l5_gdf.empty and not land_use_gdf.empty:
        l5_gdf = gpd.sjoin(l5_gdf, land_use_gdf[["geometry", "landuse_type_id"]], how='left', predicate='within')
        if "index_right" in l5_gdf.columns:
            l5_gdf.drop(columns=["index_right"], inplace=True)
    
    # 5. 计算Spacematrix指标
    if not l5_gdf.empty and not building_gdf.empty:
        print("计算Spacematrix形态指标...")
        morphology_stats = calculate_spacematrix_indicators(l5_gdf, building_gdf)
        l5_gdf = l5_gdf.merge(morphology_stats, on='land_id', how='left')
    
    # 6. 计算街区主导功能
    if not l4_gdf.empty and not poi_gdf.empty:
        print("计算街区主导功能...")
        region_functions = calculate_region_dominant_function(l4_gdf, poi_gdf)
        l4_gdf = l4_gdf.merge(region_functions, on='region_id', how='left')
    
    # 7. 处理签到数据
    print("处理签到数据...")
    checkin_gdf = gpd.GeoDataFrame()  # 简化处理，实际使用时可加载真实数据
    
    print("✅ 数据预处理完成")
    return l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf

def calculate_spacematrix_indicators(l5_gdf, building_gdf):
    """计算Spacematrix形态指标"""
    morphology_data = []
    
    for _, land in show_progress(l5_gdf.iterrows(), "计算形态指标"):
        land_buildings = building_gdf[building_gdf.get('land_id') == land['land_id']]
        
        if len(land_buildings) == 0:
            morphology_data.append({
                'land_id': land['land_id'],
                'FSI': 0, 'GSI': 0, 'OSR': 1, 'L': 0,
                'morphology_type': 'Morph_Vacant'
            })
            continue
        
        # 计算指标
        land_area = land.geometry.area
        building_area = land_buildings.geometry.area.sum()
        
        # 使用Height字段推算楼层数
        height_field = FIELD_MAPPING["building"]["height_field"]
        if height_field in land_buildings.columns:
            heights = pd.to_numeric(land_buildings[height_field], errors='coerce').fillna(PARAMS["floor_height"])
            floors = (heights / PARAMS["floor_height"]).round().clip(lower=1)
            total_floor_area = (land_buildings.geometry.area * floors).sum()
            avg_floors = floors.mean()
            max_height = heights.max()
        else:
            total_floor_area = building_area * 3
            avg_floors = 3
            max_height = PARAMS["floor_height"] * 3
        
        FSI = total_floor_area / land_area if land_area > 0 else 0
        GSI = building_area / land_area if land_area > 0 else 0
        OSR = (1 - GSI) / FSI if FSI > 0 else 1
        L = avg_floors
        
        morphology_type = classify_morphology_enhanced(FSI, GSI, OSR, L, max_height)
        
        morphology_data.append({
            'land_id': land['land_id'],
            'FSI': FSI, 'GSI': GSI, 'OSR': OSR, 'L': L,
            'morphology_type': morphology_type
        })
    
    return pd.DataFrame(morphology_data)

def calculate_region_dominant_function(l4_gdf, poi_gdf):
    """计算街区主导功能"""
    region_functions = []
    
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.get('region_id') == region.region_id]
        
        if region_pois.empty or 'main_cat' not in region_pois.columns:
            dominant_function = "RegionFunc_Mixed"
        else:
            # 统计POI类别分布
            poi_counts = region_pois['main_cat'].value_counts()
            if not poi_counts.empty:
                top_category = poi_counts.index[0]
                # 简化的功能映射
                if "餐饮" in top_category or "购物" in top_category:
                    dominant_function = "RegionFunc_Commercial"
                elif "住宅" in top_category or "生活" in top_category:
                    dominant_function = "RegionFunc_Residential"
                elif "办公" in top_category or "商务" in top_category:
                    dominant_function = "RegionFunc_Office"
                elif "教育" in top_category or "文化" in top_category:
                    dominant_function = "RegionFunc_Education"
                else:
                    dominant_function = "RegionFunc_Mixed"
            else:
                dominant_function = "RegionFunc_Mixed"
        
        region_functions.append({
            'region_id': region.region_id,
            'dominant_function_id': dominant_function
        })
    
    return pd.DataFrame(region_functions)

# ==================== 关系生成函数 ====================

def generate_hierarchical_relations(l4_gdf, l5_gdf, poi_gdf, building_gdf):
    """生成层次归属关系（3种）"""
    print_section("生成层次归属关系")
    triples = []
    
    # 1. belongsToLand - 建筑物归属地块
    print("1. belongsToLand - 建筑物归属地块...")
    count = 0
    if not building_gdf.empty:
        for _, building in show_progress(building_gdf.iterrows(), "建筑物归属地块"):
            if pd.notna(building.get('land_id')):
                triples.append((building.building_id, "belongsToLand", building.land_id))
                count += 1
    print(f"   生成 {count:,} 个 belongsToLand 三元组")
    
    # 2. belongsToRegion - 地块归属街区
    print("2. belongsToRegion - 地块归属街区...")
    count = 0
    if not l5_gdf.empty:
        for _, land in show_progress(l5_gdf.iterrows(), "地块归属街区"):
            if pd.notna(land.get('region_id')):
                triples.append((land.land_id, "belongsToRegion", land.region_id))
                count += 1
    print(f"   生成 {count:,} 个 belongsToRegion 三元组")
    
    # 3. locateAt - POI定位街区
    print("3. locateAt - POI定位街区...")
    count = 0
    if not poi_gdf.empty:
        for _, poi in show_progress(poi_gdf.iterrows(), "POI定位"):
            if pd.notna(poi.get('region_id')):
                triples.append((poi.poi_id, "locateAt", poi.region_id))
                count += 1
    print(f"   生成 {count:,} 个 locateAt 三元组")
    
    print(f"✅ 层次归属关系总计: {len(triples):,} 个三元组")
    return triples

def generate_attribute_relations(l4_gdf, l5_gdf, poi_gdf, building_gdf):
    """生成属性关联关系（6种）"""
    print_section("生成属性关联关系")
    triples = []
    
    # 4. hasFunction - 建筑物功能属性
    print("4. hasFunction - 建筑物功能属性...")
    count = 0
    if not building_gdf.empty:
        for _, building in show_progress(building_gdf.iterrows(), "建筑物功能"):
            if pd.notna(building.get('function_id')):
                triples.append((building.building_id, "hasFunction", building.function_id))
                count += 1
    print(f"   生成 {count:,} 个 hasFunction 三元组")
    
    # 5. hasMorphology - 地块形态属性
    print("5. hasMorphology - 地块形态属性...")
    count = 0
    if not l5_gdf.empty:
        for _, land in show_progress(l5_gdf.iterrows(), "地块形态"):
            if pd.notna(land.get('morphology_type')):
                triples.append((land.land_id, "hasMorphology", land.morphology_type))
                count += 1
    print(f"   生成 {count:,} 个 hasMorphology 三元组")
    
    # 6. hasLandUse - 地块土地利用属性
    print("6. hasLandUse - 地块土地利用属性...")
    count = 0
    if not l5_gdf.empty:
        for _, land in show_progress(l5_gdf.iterrows(), "地块土地利用"):
            if pd.notna(land.get('landuse_type_id')):
                triples.append((land.land_id, "hasLandUse", land.landuse_type_id))
                count += 1
    print(f"   生成 {count:,} 个 hasLandUse 三元组")
    
    # 7. hasDominantFunction - 街区主导功能
    print("7. hasDominantFunction - 街区主导功能...")
    count = 0
    if not l4_gdf.empty:
        for _, region in show_progress(l4_gdf.iterrows(), "街区功能"):
            if pd.notna(region.get('dominant_function_id')):
                triples.append((region.region_id, "hasDominantFunction", region.dominant_function_id))
                count += 1
    print(f"   生成 {count:,} 个 hasDominantFunction 三元组")
    
    # 8. cateOf - POI类别归属
    print("8. cateOf - POI类别归属...")
    count = 0
    if not poi_gdf.empty:
        for _, poi in show_progress(poi_gdf.iterrows(), "POI类别"):
            if pd.notna(poi.get('category_id')):
                triples.append((poi.poi_id, "cateOf", poi.category_id))
                count += 1
    print(f"   生成 {count:,} 个 cateOf 三元组")
    
    # 9. hasPhysicalAttribute - 建筑物物理属性（分类版）
    print("9. hasPhysicalAttribute - 建筑物物理属性...")
    count = 0
    if not building_gdf.empty:
        for _, building in show_progress(building_gdf.iterrows(), "建筑物物理属性"):
            if pd.notna(building.get('physical_attr_id')):
                triples.append((building.building_id, "hasPhysicalAttribute", building.physical_attr_id))
                count += 1
    print(f"   生成 {count:,} 个 hasPhysicalAttribute 三元组")
    
    print(f"✅ 属性关联关系总计: {len(triples):,} 个三元组")
    return triples

def generate_spatial_relations(l4_gdf, l5_gdf, building_gdf):
    """生成同层次空间关系（6种）"""
    print_section("生成同层次空间关系")
    triples = []
    
    # 10. connectedTo - 建筑物间连接
    print("10. connectedTo - 建筑物间连接...")
    count = 0
    if not building_gdf.empty:
        # 限制处理数量以提高效率
        max_buildings = min(len(building_gdf), 1000)
        buildings_sample = building_gdf.head(max_buildings)
        
        building_pairs = [(i, j, b1, b2) for i, b1 in enumerate(buildings_sample.itertuples()) 
                         for j, b2 in enumerate(buildings_sample.itertuples()) if i < j]
        
        for i, j, b1, b2 in show_progress(building_pairs[:5000], "建筑物连接"):  # 限制连接数
            distance = b1.geometry.distance(b2.geometry)
            if distance < PARAMS["building_connection_distance"]:
                triples.append((b1.building_id, "connectedTo", b2.building_id))
                count += 1
    print(f"   生成 {count:,} 个 connectedTo 三元组")
    
    # 11. adjacentTo - 地块邻接关系
    print("11. adjacentTo - 地块邻接关系...")
    count = 0
    if not l5_gdf.empty:
        max_lands = min(len(l5_gdf), 300)  # 限制处理数量
        lands_sample = l5_gdf.head(max_lands)
        
        land_pairs = [(i, j, l1, l2) for i, l1 in enumerate(lands_sample.itertuples()) 
                     for j, l2 in enumerate(lands_sample.itertuples()) if i < j]
        
        for i, j, l1, l2 in show_progress(land_pairs, "地块邻接"):
            if l1.geometry.touches(l2.geometry):
                triples.append((l1.land_id, "adjacentTo", l2.land_id))
                count += 1
    print(f"   生成 {count:,} 个 adjacentTo 三元组")
    
    # 12. borderBy - 街区边界相接
    print("12. borderBy - 街区边界相接...")
    count = 0
    if not l4_gdf.empty:
        region_pairs = [(i, j, r1, r2) for i, r1 in enumerate(l4_gdf.itertuples()) 
                       for j, r2 in enumerate(l4_gdf.itertuples()) if i < j]
        
        for i, j, r1, r2 in show_progress(region_pairs, "街区边界"):
            if r1.geometry.touches(r2.geometry):
                intersection = r1.geometry.intersection(r2.geometry)
                if hasattr(intersection, 'length') and intersection.length > PARAMS["border_buffer"]:
                    triples.append((r1.region_id, "borderBy", r2.region_id))
                    triples.append((r2.region_id, "borderBy", r1.region_id))  # 双向关系
                    count += 2
    print(f"   生成 {count:,} 个 borderBy 三元组")
    
    # 13. nearBy - 街区近距离关系
    print("13. nearBy - 街区近距离关系...")
    count = 0
    if not l4_gdf.empty:
        l4_centroids = l4_gdf.copy()
        l4_centroids.geometry = l4_centroids.geometry.centroid
        
        centroid_pairs = [(i, j, r1, r2) for i, r1 in enumerate(l4_centroids.itertuples()) 
                         for j, r2 in enumerate(l4_centroids.itertuples()) if i < j]
        
        for i, j, r1, r2 in show_progress(centroid_pairs, "街区近距离"):
            distance = r1.geometry.distance(r2.geometry)
            if distance <= PARAMS["nearby_distance"] and not r1.geometry.touches(r2.geometry):
                triples.append((r1.region_id, "nearBy", r2.region_id))
                triples.append((r2.region_id, "nearBy", r1.region_id))  # 双向关系
                count += 2
    print(f"   生成 {count:,} 个 nearBy 三元组")
    
    # 14. similarMorphology - 地块形态相似
    print("14. similarMorphology - 地块形态相似...")
    count = 0
    if not l5_gdf.empty and 'morphology_type' in l5_gdf.columns:
        morphology_groups = l5_gdf.groupby('morphology_type')
        
        for morph_type, group in morphology_groups:
            if len(group) > 1:
                lands = list(group.itertuples())
                # 限制每组的连接数量
                max_connections = min(len(lands), 20)
                
                for i in range(max_connections):
                    for j in range(i+1, min(max_connections, len(lands))):
                        distance = lands[i].geometry.distance(lands[j].geometry)
                        if distance < 300:  # 300米内的相似形态地块
                            triples.append((lands[i].land_id, "similarMorphology", lands[j].land_id))
                            count += 1
    print(f"   生成 {count:,} 个 similarMorphology 三元组")
    
    # 15. similarFunction - 建筑物功能相似
    print("15. similarFunction - 建筑物功能相似...")
    count = 0
    if not building_gdf.empty and 'function_id' in building_gdf.columns:
        function_groups = building_gdf.groupby('function_id')
        
        for func_type, group in function_groups:
            if len(group) > 1:
                buildings = list(group.itertuples())
                # 限制每组的连接数量
                max_connections = min(len(buildings), 15)
                
                for i in range(max_connections):
                    for j in range(i+1, min(max_connections, len(buildings))):
                        distance = buildings[i].geometry.distance(buildings[j].geometry)
                        if distance < 200:  # 200米内的相似功能建筑
                            triples.append((buildings[i].building_id, "similarFunction", buildings[j].building_id))
                            count += 1
    print(f"   生成 {count:,} 个 similarFunction 三元组")
    
    print(f"✅ 同层次空间关系总计: {len(triples):,} 个三元组")
    return triples

def generate_functional_relations(l4_gdf, poi_gdf, building_gdf):
    """生成跨层次功能关系（4种）"""
    print_section("生成跨层次功能关系")
    triples = []
    
    # 16. functionalSimilarity - 街区功能相似性
    print("16. functionalSimilarity - 街区功能相似性...")
    count = 0
    
    # 计算POI分布向量
    distributions, all_categories = calculate_poi_distribution(l4_gdf, poi_gdf)
    region_ids = list(distributions.keys())
    
    if len(all_categories) > 0:
        vectors = np.array([distributions[rid] for rid in region_ids])
        
        if len(vectors) > 0 and vectors.shape[1] > 0:
            region_pairs = [(i, j) for i in range(len(region_ids)) for j in range(i+1, len(region_ids))]
            
            for i, j in show_progress(region_pairs, "街区功能相似"):
                if np.sum(vectors[i]) > 0 and np.sum(vectors[j]) > 0:
                    similarity = 1 - cosine(vectors[i], vectors[j])
                    if similarity >= PARAMS["function_similarity_threshold"]:
                        triples.append((region_ids[i], "functionalSimilarity", region_ids[j]))
                        count += 1
    print(f"   生成 {count:,} 个 functionalSimilarity 三元组")
    
    # 17. highConvenience - 街区便利性关联
    print("17. highConvenience - 街区便利性关联...")
    count = 0
    convenience_regions = []
    
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.get('region_id') == region.region_id] if not poi_gdf.empty else pd.DataFrame()
        is_convenient, score = check_convenience(region_pois)
        if is_convenient:
            convenience_regions.append(region.region_id)
    
    if len(convenience_regions) > 1:
        l4_centroids = l4_gdf.copy()
        l4_centroids.geometry = l4_centroids.geometry.centroid
        
        for i in range(len(convenience_regions)):
            for j in range(i+1, len(convenience_regions)):
                r1_geom = l4_centroids[l4_centroids.region_id == convenience_regions[i]].geometry.iloc[0]
                r2_geom = l4_centroids[l4_centroids.region_id == convenience_regions[j]].geometry.iloc[0]
                distance = r1_geom.distance(r2_geom)
                
                if distance < PARAMS["convenience_distance_threshold"]:
                    triples.append((convenience_regions[i], "highConvenience", convenience_regions[j]))
                    count += 1
    print(f"   生成 {count:,} 个 highConvenience 三元组")
    
    # 18. functionalComplementarity - 街区功能互补
    print("18. functionalComplementarity - 街区功能互补...")
    count = 0
    
    # 计算各区域的主要POI类别
    region_main_categories = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.get('region_id') == region.region_id] if not poi_gdf.empty else pd.DataFrame()
        if not region_pois.empty and 'main_cat' in region_pois.columns:
            main_cat = region_pois['main_cat'].mode()
            if not main_cat.empty:
                region_main_categories[region.region_id] = main_cat.iloc[0]
    
    # 基于功能互补配置生成关系
    for source_cat, target_cat in FUNCTIONAL_COMPLEMENTS:
        source_regions = [rid for rid, cat in region_main_categories.items() if cat == source_cat]
        target_regions = [rid for rid, cat in region_main_categories.items() if cat == target_cat]
        
        for src_rid in source_regions:
            for tgt_rid in target_regions:
                if src_rid != tgt_rid:
                    src_geom = l4_gdf[l4_gdf.region_id == src_rid].geometry.centroid.iloc[0]
                    tgt_geom = l4_gdf[l4_gdf.region_id == tgt_rid].geometry.centroid.iloc[0]
                    distance = src_geom.distance(tgt_geom)
                    
                    if distance < 1500:  # 1.5公里内的功能互补
                        triples.append((src_rid, "functionalComplementarity", tgt_rid))
                        count += 1
    print(f"   生成 {count:,} 个 functionalComplementarity 三元组")
    
    # 19. densityInfluence - 密度影响关系
    print("19. densityInfluence - 密度影响关系...")
    count = 0
    
    # 计算各区域的建筑密度
    region_density = {}
    for _, region in l4_gdf.iterrows():
        region_buildings = building_gdf[building_gdf.get('region_id') == region.region_id] if not building_gdf.empty else pd.DataFrame()
        building_count = len(region_buildings)
        area_km2 = region.geometry.area / 1000000
        density = building_count / area_km2 if area_km2 > 0 else 0
        region_density[region.region_id] = density
    
    # 识别高密度和低密度区域
    density_values = list(region_density.values())
    if len(density_values) > 0:
        density_threshold = np.percentile(density_values, 70)  # 前30%为高密度
        
        high_density_regions = [rid for rid, density in region_density.items() if density >= density_threshold]
        low_density_regions = [rid for rid, density in region_density.items() if density < density_threshold]
        
        for high_rid in high_density_regions:
            for low_rid in low_density_regions:
                high_geom = l4_gdf[l4_gdf.region_id == high_rid].geometry.centroid.iloc[0]
                low_geom = l4_gdf[l4_gdf.region_id == low_rid].geometry.centroid.iloc[0]
                distance = high_geom.distance(low_geom)
                
                if distance < PARAMS["density_influence_distance"]:
                    density_ratio = region_density[high_rid] / (region_density[low_rid] + 0.1)
                    if density_ratio >= PARAMS["density_influence_threshold"]:
                        triples.append((high_rid, "densityInfluence", low_rid))
                        count += 1
    print(f"   生成 {count:,} 个 densityInfluence 三元组")
    
    print(f"✅ 跨层次功能关系总计: {len(triples):,} 个三元组")
    return triples

def generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf):
    """生成移动性关系（1种）"""
    print_section("生成移动性关系")
    triples = []
    
    # 20. flowTransition - 街区间人流移动
    print("20. flowTransition - 街区间人流移动...")
    count = 0
    
    # 基于流动模式生成模拟流动
    region_categories = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.get('region_id') == region.region_id] if not poi_gdf.empty else pd.DataFrame()
        categories = set()
        if not region_pois.empty and 'main_cat' in region_pois.columns:
            categories = set(region_pois['main_cat'].dropna())
        region_categories[region.region_id] = categories
    
    for source_cat, target_cat in show_progress(FLOW_PATTERNS, "流动模式"):
        source_regions = [rid for rid, cats in region_categories.items() if source_cat in cats]
        target_regions = [rid for rid, cats in region_categories.items() if target_cat in cats]
        
        for source_id in source_regions:
            for target_id in target_regions:
                if source_id != target_id:
                    source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
                    target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
                    distance = source_geom.distance(target_geom)
                    
                    if distance < PARAMS["flow_distance_threshold"]:
                        triples.append((source_id, "flowTransition", target_id))
                        count += 1
    
    print(f"   生成 {count:,} 个 flowTransition 三元组")
    print(f"✅ 移动性关系总计: {len(triples):,} 个三元组")
    return triples

def generate_service_relations(l4_gdf, bc_gdf):
    """生成服务关系（1种）"""
    print_section("生成服务关系")
    triples = []
    
    # 21. provideService - 商圈服务街区
    print("21. provideService - 商圈服务街区...")
    count = 0
    
    if not bc_gdf.empty:
        for _, bc in show_progress(bc_gdf.iterrows(), "商圈服务"):
            for _, region in l4_gdf.iterrows():
                if (bc.geometry.contains(region.geometry.centroid) or 
                    bc.geometry.intersects(region.geometry)):
                    triples.append((bc.bc_id, "provideService", region.region_id))
                    count += 1
                else:
                    distance = bc.geometry.centroid.distance(region.geometry.centroid)
                    if distance <= 3000:  # 3公里服务半径
                        triples.append((bc.bc_id, "provideService", region.region_id))
                        count += 1
    
    print(f"   生成 {count:,} 个 provideService 三元组")
    print(f"✅ 服务关系总计: {len(triples):,} 个三元组")
    return triples

# ==================== 辅助函数 ====================

def calculate_poi_distribution(l4_gdf, poi_gdf):
    """计算每个区域的POI类别分布向量"""
    if poi_gdf.empty or 'main_cat' not in poi_gdf.columns:
        return {}, []
    
    all_categories = poi_gdf["main_cat"].dropna().unique()
    category_to_idx = {cat: i for i, cat in enumerate(all_categories)}
    
    distributions = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.get('region_id') == region.region_id]
        distribution = np.zeros(len(all_categories))
        
        if not region_pois.empty:
            for _, poi in region_pois.iterrows():
                category = poi.get('main_cat')
                if pd.notna(category) and category in category_to_idx:
                    distribution[category_to_idx[category]] += 1
            
            if distribution.sum() > 0:
                distribution = distribution / distribution.sum()
        
        distributions[region.region_id] = distribution
    
    return distributions, all_categories

def check_convenience(region_pois):
    """判断街区功能便利性"""
    if region_pois.empty:
        return False, 0
    
    # 基本统计
    total_pois = len(region_pois)
    if 'main_cat' not in region_pois.columns:
        return False, 0
        
    categories = set(region_pois['main_cat'].dropna())
    category_count = len(categories)
    
    # 检查基本条件
    if (category_count < PARAMS["convenience_min_categories"] or 
        total_pois < PARAMS["convenience_min_pois"]):
        return False, 0
    
    # 检查基本服务类别覆盖
    essential_cats = set(PARAMS["convenience_essential_categories"])
    covered_essential = essential_cats.intersection(categories)
    if len(covered_essential) < 3:
        return False, 0
    
    # 计算便利性得分
    convenience_score = 0
    convenience_score += category_count
    convenience_score += len(covered_essential) * 2
    
    if total_pois > 40:
        convenience_score += 3
    elif total_pois > 25:
        convenience_score += 2
    
    if len(covered_essential) >= 3:
        convenience_score += 2
    
    is_convenient = convenience_score >= PARAMS["convenience_score_threshold"]
    
    return is_convenient, convenience_score

# ==================== 保存和统计函数 ====================

def save_triples_with_stats(triples, output_path):
    """保存三元组并生成统计信息"""
    print_section("保存三元组和统计")
    
    # 去重
    unique_triples = list(set(triples))
    print(f"去重前: {len(triples):,} 个三元组")
    print(f"去重后: {len(unique_triples):,} 个三元组")
    
    # 保存三元组
    ensure_dir(output_path)
    with open(output_path, 'w', encoding='utf-8') as f:
        for head, relation, tail in show_progress(unique_triples, "保存三元组"):
            f.write(f"{head}\t{relation}\t{tail}\n")
    
    # 统计关系类型
    relation_counts = {}
    entity_stats = defaultdict(int)
    
    for head, relation, tail in unique_triples:
        relation_counts[relation] = relation_counts.get(relation, 0) + 1
        
        # 统计实体类型
        for entity in [head, tail]:
            if entity.startswith("Region_"):
                entity_stats["Region"] += 1
            elif entity.startswith("Land_"):
                entity_stats["Land"] += 1
            elif entity.startswith("Building_"):
                entity_stats["Building"] += 1
            elif entity.startswith("POI_"):
                entity_stats["POI"] += 1
            elif entity.startswith("BC_"):
                entity_stats["BusinessCircle"] += 1
            elif entity.startswith("Morph_"):
                entity_stats["Morphology"] += 1
            elif entity.startswith("Func_"):
                entity_stats["Function"] += 1
            elif entity.startswith("Cate_"):
                entity_stats["Category"] += 1
            elif entity.startswith("LandUse_"):
                entity_stats["LandUse"] += 1
            elif entity.startswith("PhysicalAttr_"):
                entity_stats["PhysicalAttribute"] += 1
            elif entity.startswith("RegionFunc_"):
                entity_stats["RegionFunction"] += 1
    
    # 保存统计信息
    stats_df = pd.DataFrame([
        {'relation_type': rel, 'count': count, 'percentage': count/len(unique_triples)*100}
        for rel, count in sorted(relation_counts.items(), key=lambda x: x[1], reverse=True)
    ])
    
    ensure_dir(OUTPUT_PATHS["relation_stats"])
    stats_df.to_csv(OUTPUT_PATHS["relation_stats"], index=False)
    
    # 打印统计信息
    print_section("知识图谱统计")
    print(f"三元组总数: {len(unique_triples):,}")
    print(f"关系类型数: {len(relation_counts)}")
    
    print("\n实体类型分布:")
    for entity_type, count in sorted(entity_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {entity_type:<20}: {count:>10,}")
    
    print("\n各关系类型分布:")
    for _, row in stats_df.iterrows():
        print(f"  {row['relation_type']:<30}: {row['count']:>7,} ({row['percentage']:>5.1f}%)")
    
    return unique_triples, relation_counts

def analyze_physical_attributes(building_gdf):
    """分析建筑物物理属性分布"""
    if building_gdf.empty or 'physical_attr_id' not in building_gdf.columns:
        return
    
    print_section("建筑物物理属性分析")
    
    # 统计各属性组合的分布
    attr_counts = building_gdf['physical_attr_id'].value_counts()
    
    # 分解各维度统计
    area_stats = defaultdict(int)
    height_stats = defaultdict(int)
    age_stats = defaultdict(int)
    
    for attr_id, count in attr_counts.items():
        if attr_id.startswith(PARAMS['physical_attr_prefix']):
            attr_part = attr_id.replace(PARAMS['physical_attr_prefix'], '')
            if len(attr_part) >= 9:  # LargeHighNew格式
                area_class = None
                height_class = None
                age_class = None
                
                # 解析面积
                if attr_part.startswith('Large'):
                    area_class = 'Large'
                    remaining = attr_part[5:]
                elif attr_part.startswith('Medium'):
                    area_class = 'Medium'
                    remaining = attr_part[6:]
                elif attr_part.startswith('Small'):
                    area_class = 'Small'
                    remaining = attr_part[5:]
                
                # 解析高度
                if remaining.startswith('High'):
                    height_class = 'High'
                    remaining = remaining[4:]
                elif remaining.startswith('Mid'):
                    height_class = 'Mid'
                    remaining = remaining[3:]
                elif remaining.startswith('Low'):
                    height_class = 'Low'
                    remaining = remaining[3:]
                
                # 解析年代
                if remaining == 'New':
                    age_class = 'New'
                elif remaining == 'Mid':
                    age_class = 'Mid'
                elif remaining == 'Old':
                    age_class = 'Old'
                
                if area_class:
                    area_stats[area_class] += count
                if height_class:
                    height_stats[height_class] += count
                if age_class:
                    age_stats[age_class] += count
    
    # 保存统计结果
    physical_attr_df = pd.DataFrame([
        {'attribute_combination': attr, 'count': count, 'percentage': count/len(building_gdf)*100}
        for attr, count in attr_counts.head(15).items()
    ])
    
    ensure_dir(OUTPUT_PATHS["physical_attr_stats"])
    physical_attr_df.to_csv(OUTPUT_PATHS["physical_attr_stats"], index=False)
    
    # 打印统计信息
    print("建筑物物理属性组合分布（前15种）:")
    for _, row in physical_attr_df.iterrows():
        print(f"  {row['attribute_combination']:<30}: {row['count']:>5} ({row['percentage']:>5.1f}%)")
    
    print(f"\n各维度分布:")
    print(f"面积分布: {dict(area_stats)}")
    print(f"高度分布: {dict(height_stats)}")
    print(f"年代分布: {dict(age_stats)}")

# ==================== 主函数 ====================

def main():
    """主函数"""
    try:
        print_section("🚀 优化版空间多层次知识图谱生成器")
        print("🎯 优化特点:")
        print("   ✅ 21种关系类型，层次清晰")
        print("   ✅ 建筑物物理属性分类化（面积×高度×年代=27种组合）")
        print("   ✅ 三层空间嵌套：建筑物→地块→街区")
        print("   ✅ 增强连通性，减少孤立节点")
        print(f"📂 输出目录: {OUTPUT_BASE_PATH}")
        
        start_time = time.time()
        
        # 1. 数据加载
        l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf = load_all_data()
        
        # 2. 数据预处理
        l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf = \
            preprocess_all_data(l4_gdf, l5_gdf, poi_gdf, bc_gdf, checkin_gdf, building_gdf, land_use_gdf)
        
        # 3. 生成各类关系
        all_triples = []
        
        print_section("🔗 开始生成21种关系类型")
        
        # 关系生成任务
        relation_tasks = [
            ("层次归属关系(3种)", lambda: generate_hierarchical_relations(l4_gdf, l5_gdf, poi_gdf, building_gdf)),
            ("属性关联关系(6种)", lambda: generate_attribute_relations(l4_gdf, l5_gdf, poi_gdf, building_gdf)),
            ("同层空间关系(6种)", lambda: generate_spatial_relations(l4_gdf, l5_gdf, building_gdf)),
            ("跨层功能关系(4种)", lambda: generate_functional_relations(l4_gdf, poi_gdf, building_gdf)),
            ("移动性关系(1种)", lambda: generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf)),
            ("服务关系(1种)", lambda: generate_service_relations(l4_gdf, bc_gdf))
        ]
        
        for task_name, task_func in relation_tasks:
            task_start = time.time()
            triples = task_func()
            all_triples.extend(triples)
            task_time = time.time() - task_start
            print(f"✅ {task_name}: {len(triples):,} 个三元组 (耗时: {task_time:.1f}s)")
        
        # 4. 保存结果并统计
        unique_triples, final_relation_counts = save_triples_with_stats(
            all_triples, OUTPUT_PATHS["kg_optimized"]
        )
        
        # 5. 分析建筑物物理属性
        analyze_physical_attributes(building_gdf)
        
        # 6. 连通性分析
        total_entities = len(l4_gdf) + len(l5_gdf) + len(building_gdf) + len(poi_gdf)
        avg_connections = len(unique_triples) / total_entities if total_entities > 0 else 0
        
        print_section("🎉 生成完成")
        total_time = time.time() - start_time
        print(f"✅ 总耗时: {total_time:.1f} 秒")
        print(f"✅ 处理速度: {len(unique_triples)/total_time:.0f} 三元组/秒")
        print(f"✅ 平均连接度: {avg_connections:.2f} 连接/实体")
        print(f"✅ 知识图谱已保存: {OUTPUT_PATHS['kg_optimized']}")
        
        print_section("📋 最终效果评估")
        print("✅ 层次结构:")
        print("   - 三层空间嵌套：建筑物→地块→街区")
        print("   - 清晰的归属关系链")
        print("✅ 属性丰富:")
        print("   - 建筑物：功能+物理属性（27种组合）")
        print("   - 地块：形态+土地利用")
        print("   - 街区：主导功能")
        print("✅ 连通性强:")
        print(f"   - 21种关系类型")
        print(f"   - 平均每实体{avg_connections:.1f}个连接")
        print("   - 多路径连通，适合GNN训练")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成知识图谱时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
