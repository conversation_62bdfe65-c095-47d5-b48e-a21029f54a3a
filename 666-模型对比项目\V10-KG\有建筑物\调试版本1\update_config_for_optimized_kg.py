#!/usr/bin/env python3
"""
配置更新脚本 - 使用优化后的知识图谱

这个脚本会：
1. 备份原始配置文件
2. 更新配置以使用优化后的知识图谱
3. 提供回滚选项
"""

import shutil
from pathlib import Path
import json

def backup_original_config():
    """备份原始配置文件"""
    config_path = Path("./训练评估/config.py")
    backup_path = Path("./训练评估/config_backup.py")
    
    if config_path.exists():
        shutil.copy2(config_path, backup_path)
        print(f"✅ 原始配置已备份到: {backup_path}")
        return True
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        return False

def update_config_for_optimized_kg():
    """更新配置文件以使用优化后的知识图谱"""
    config_path = Path("./训练评估/config.py")
    
    # 读取原始配置
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换知识图谱路径
    old_kg_path = "'kg_path': './data/shenyang/kg_cuda_complete_21_relations_optimized.txt'"
    new_kg_path = "'kg_path': '../optimized_kg/kg_optimized_for_rgcn.txt'"
    
    updated_content = content.replace(old_kg_path, new_kg_path)
    
    # 写入更新后的配置
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✅ 配置文件已更新: {config_path}")
    print(f"   知识图谱路径已更改为: ../optimized_kg/kg_optimized_for_rgcn.txt")

def create_optimized_config():
    """创建专门用于优化知识图谱的配置文件"""
    config_content = '''def get_config(dataset='shenyang'):
    """获取数据集配置 - 优化版本"""
    configs = {
        'shenyang': {
            'kg_path': '../optimized_kg/kg_optimized_for_rgcn.txt',  # 使用优化后的知识图谱
            'region_info_path': './data/shenyang/shenyang_region2allinfo.json',
            'train_path': './data/shenyang/shenyang_zl15_train.csv',
            'val_path': './data/shenyang/shenyang_zl15_valid.csv',
            'test_path': './data/shenyang/shenyang_zl15_test.csv',
            'pretrain_path': './data/shenyang/ER_shenhe_TuckER_64.npz'
        }
    }
    
    if dataset not in configs:
        raise ValueError(f"Unknown dataset: {dataset}")
    
    return configs[dataset]
'''
    
    config_path = Path("./训练评估/config_optimized.py")
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 优化配置文件已创建: {config_path}")

def restore_original_config():
    """恢复原始配置文件"""
    config_path = Path("./训练评估/config.py")
    backup_path = Path("./训练评估/config_backup.py")
    
    if backup_path.exists():
        shutil.copy2(backup_path, config_path)
        print(f"✅ 原始配置已恢复: {config_path}")
        return True
    else:
        print(f"❌ 备份文件不存在: {backup_path}")
        return False

def show_optimization_results():
    """显示优化结果对比"""
    report_path = Path("./optimized_kg/optimization_report.json")
    
    if report_path.exists():
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        print("\n📊 知识图谱优化结果对比:")
        print("=" * 50)
        
        summary = report['optimization_summary']
        
        print("🔍 优化前 vs 优化后:")
        print(f"   连通性比例: 0.004 → {summary['connectivity_ratio']:.3f} (提升 {summary['connectivity_ratio']/0.004:.1f}倍)")
        print(f"   关系分布基尼系数: 0.698 → {summary['relation_gini']:.3f} (改善 {(0.698-summary['relation_gini'])/0.698*100:.1f}%)")
        print(f"   区域平均连接度: 709.8 → {summary['avg_region_connections']:.1f}")
        print(f"   关系类型数量: 19 → {summary['num_relations']}")
        
        print("\n🔗 关系分布:")
        relation_dist = report['relation_distribution']
        for relation, count in sorted(relation_dist.items(), key=lambda x: x[1], reverse=True):
            percentage = count / summary['total_triples'] * 100
            print(f"   - {relation}: {count:,} ({percentage:.1f}%)")
        
        print(f"\n📈 预期效果:")
        print(f"   - 图连通性显著提升 (10倍改善)")
        print(f"   - 关系分布更加均衡")
        print(f"   - RGCN模型R²预期从0.05提升到0.25左右")
    else:
        print(f"❌ 优化报告不存在: {report_path}")

def main():
    """主函数"""
    print("🚀 知识图谱配置更新工具")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 备份原始配置并更新为优化版本")
        print("2. 创建独立的优化配置文件")
        print("3. 恢复原始配置")
        print("4. 查看优化结果")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            print("\n📋 备份并更新配置...")
            if backup_original_config():
                update_config_for_optimized_kg()
                print("\n✅ 配置更新完成！现在可以使用优化后的知识图谱训练模型了。")
        
        elif choice == '2':
            print("\n📋 创建优化配置文件...")
            create_optimized_config()
            print("\n💡 提示: 可以通过修改导入语句使用优化配置:")
            print("   from config_optimized import get_config")
        
        elif choice == '3':
            print("\n📋 恢复原始配置...")
            restore_original_config()
        
        elif choice == '4':
            show_optimization_results()
        
        elif choice == '5':
            print("\n👋 再见！")
            break
        
        else:
            print("\n❌ 无效选择，请重新输入。")

if __name__ == "__main__":
    main()
