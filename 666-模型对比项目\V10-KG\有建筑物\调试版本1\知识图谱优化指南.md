# 知识图谱优化指南 - 提升RGCN模型R²性能

## 🎯 目标
在保持757个区域的同时，将RGCN模型的R²从0.05提升到0.25左右

## 📊 问题分析

### 当前知识图谱A的主要问题：
1. **连通性极差**：最大强连通分量只占0.4%（737/200,558个节点）
2. **图结构过于稀疏**：强连通分量数高达199,817个，几乎每个节点都是孤立的
3. **关系分布不均衡**：基尼系数0.698，关系类型分布极不均匀
4. **区域连接度异常**：15个区域连接度异常低，平均连接度688.73但方差很大

### 知识图谱B的成功经验：
1. **更好的连通性**：虽然也不是强连通，但结构相对更紧密
2. **更均衡的关系分布**：基尼系数0.619，比A更均衡
3. **更丰富的关系类型**：包含建筑物相关关系（hasFunctionBuilding, belongsToBuilding等）
4. **更合理的区域连接度**：平均437.11，方差相对较小

## 🚀 优化策略

### 1. 保留高质量关系
保留原有的核心关系类型：
- `cateOf`: POI类别关系
- `locateAt`: 位置关系
- `hasFunction`: 功能关系
- `hasPhysicalAttribute`: 物理属性关系
- `belongsToLand`: 土地归属关系
- `nearBy`: 邻近关系
- `highConvenience`: 便利性关系

### 2. 增强空间连接
基于地理位置创建区域间的空间邻近关系：
- 计算区域中心点间的欧几里得距离
- 为距离小于阈值的区域添加`spatiallyNear`关系
- 双向连接以增强连通性

### 3. 添加功能相似性连接
基于POI类别分布创建功能相似性关系：
- 计算区域间POI类别向量的余弦相似度
- 为高相似度区域添加`functionalSimilarity`关系
- 阈值设为0.7以确保高质量连接

### 4. 引入建筑物层次结构
借鉴知识图谱B的成功经验：
- 为每个有POI的区域创建虚拟建筑物实体
- 添加`hasBuilding`和`withinRegion`关系
- 基于POI类别创建功能建筑物
- 添加`hasFunctionBuilding`和`belongsToBuilding`关系

### 5. 平衡关系分布
- 控制各关系类型的数量比例
- 优先保留重要关系类型
- 避免某些关系过度占主导地位

## 🛠️ 使用方法

### 1. 运行优化脚本
```bash
cd 666-模型对比项目/V10-KG/有建筑物
python kg_optimization_strategy.py
```

### 2. 检查输出文件
优化后会在`./optimized_kg/`目录下生成：
- `kg_optimized_for_rgcn.txt`: 优化后的知识图谱文件
- `optimization_report.json`: 详细的优化报告

### 3. 更新配置文件
修改`config.py`中的知识图谱路径：
```python
'kg_path': './optimized_kg/kg_optimized_for_rgcn.txt'
```

### 4. 重新训练RGCN模型
使用优化后的知识图谱重新训练模型，观察R²性能提升

## 📈 预期效果

### 优化目标指标：
- **连通性比例**: 从0.4%提升到>20%
- **区域平均连接度**: 保持在200-500范围内
- **关系分布基尼系数**: 从0.698降低到<0.6
- **RGCN模型R²**: 从0.05提升到0.25左右

### 关键改进点：
1. **增强图连通性**: 通过空间和功能连接减少孤立节点
2. **平衡关系分布**: 避免某些关系过度占主导
3. **引入层次结构**: 通过建筑物实体增加图的表达能力
4. **保持区域数量**: 维持757个区域不变

## 🔧 参数调优

如果初次优化效果不理想，可以调整以下参数：

### 空间连接参数
```python
'spatial_connection_radius': 0.01  # 增大以增加空间连接
```

### 功能相似性阈值
```python
similarity_threshold = 0.7  # 降低以增加功能连接
```

### 区域连接度控制
```python
'min_region_connections': 50   # 最少连接数
'max_region_connections': 1000 # 最多连接数
```

## 📋 验证步骤

1. **运行数据诊断**: 使用`data_diagnosis.py`检查优化后的知识图谱质量
2. **训练RGCN模型**: 使用优化后的知识图谱训练模型
3. **对比性能**: 比较优化前后的R²指标
4. **迭代优化**: 根据结果调整参数并重新优化

## 💡 进一步优化建议

如果R²仍未达到目标，可以考虑：

1. **增加更多关系类型**: 如交通便利性、人口密度影响等
2. **引入时间维度**: 添加时间相关的动态关系
3. **细化空间粒度**: 使用更精细的空间划分
4. **集成外部数据**: 引入更多城市基础设施数据

## 🎉 预期结果

通过这套优化策略，预期能够：
- 显著提升知识图谱的连通性和结构质量
- 改善RGCN模型的学习效果
- 将R²性能从0.05提升到0.25左右
- 为后续的模型优化奠定良好基础
