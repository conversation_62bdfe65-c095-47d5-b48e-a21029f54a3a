def get_config(dataset='shenyang'):
    """获取数据集配置"""
    configs = {
        'shenyang': {
            'kg_path': './data/shenyang/kg_without_building_optimized.txt',
            'region_info_path': './data/shenyang/shenyang_region2allinfo.json',
            'train_path': './data/shenyang/shenyang_zl15_train.csv',
            'val_path': './data/shenyang/shenyang_zl15_valid.csv',
            'test_path': './data/shenyang/shenyang_zl15_test.csv',
            'pretrain_path': './data/shenyang/ER_shenhe_TuckER_64.npz'
        }
    }
    
    if dataset not in configs:
        raise ValueError(f"Unknown dataset: {dataset}")
    
    return configs[dataset]