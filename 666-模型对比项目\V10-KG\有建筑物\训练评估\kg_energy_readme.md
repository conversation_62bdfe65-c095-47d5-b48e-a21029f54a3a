# 基于知识图谱的城市能耗预测系统

## 📋 项目简介

本项目是一个基于知识图谱和图神经网络的城市区域能耗预测系统，专注于沈阳市的能耗分析。系统集成了数据质量检查、知识图谱构建、特征提取和能耗预测等完整功能模块。

### 🎯 主要功能

- **知识图谱构建**: 基于城市POI、区域关系等构建多关系知识图谱
- **数据质量分析**: 全面的数据完整性、异常值检测和质量评估
- **图神经网络**: 支持RGCN、GraphSAGE等多种GNN模型
- **能耗预测**: 结合图嵌入特征进行精准能耗预测
- **可视化报告**: 生成详细的数据分析和模型性能报告

## 🛠️ 环境要求
python main.py --compare_all
python main.py --compare_all
python main.py --compare_all
python main.py --compare_all
### Python版本
- Python 3.7+

### 核心依赖
```bash
# 深度学习框架
torch>=1.8.0
dgl>=0.8.0

# 数据处理
numpy>=1.19.0
pandas>=1.2.0
scikit-learn>=0.24.0

# 图分析
networkx>=2.5
scipy>=1.6.0

# 可视化
matplotlib>=3.3.0
seaborn>=0.11.0

# 其他工具
tqdm>=4.60.0
pillow>=8.0.0
```

### 安装命令
```bash
# 创建虚拟环境
conda create -n kg_energy python=3.8
conda activate kg_energy

# 安装PyTorch (根据CUDA版本选择)
pip install torch torchvision torchaudio

# 安装DGL
pip install dgl-cu111  # CUDA 11.1版本，根据实际CUDA版本调整

# 安装其他依赖
pip install numpy pandas scikit-learn networkx scipy matplotlib seaborn tqdm pillow
```

## 📁 项目结构

```
├── main.py                    # 主训练脚本
├── config.py                  # 配置文件
├── load_data.py              # 数据加载模块
├── cal.py                    # 计算工具函数
├── data_diagnosis.py         # 数据质量检查工具
├── models/                   # 模型定义目录
│   ├── rgcn.py              # RGCN模型
│   └── graphsage.py         # GraphSAGE模型
├── utils/                    # 工具函数目录
│   └── mlp.py               # MLP预测器
├── data/                     # 数据目录
│   └── shenyang/            # 沈阳数据集
│       ├── kg_without_building_optimized.txt        # 知识图谱文件
│       ├── shenyang_region2allinfo.json            # 区域信息文件
│       ├── shenyang_zl15_train.csv                 # 训练集
│       ├── shenyang_zl15_valid.csv                 # 验证集
│       ├── shenyang_zl15_test.csv                  # 测试集
│       └── ER_shenhe_TuckER_64.npz                 # 预训练嵌入
├── saved_models/             # 模型保存目录
├── quality_analysis/         # 数据质量分析输出目录
└── README.md                # 项目说明文档
```

## 🚀 快速开始

### 1. 数据质量检查（推荐首先执行）

运行comprehensive数据质量分析工具：

```bash
python data_diagnosis.py
```

这将会：
- ✅ 检查数据完整性和对齐情况
- 🔍 分析知识图谱结构和连通性
- 📊 检测特征异常值和数据分布
- 📈 生成详细的可视化报告
- 🔧 提供自动修复建议代码

输出文件位于 `quality_analysis/` 目录：
- `comprehensive_quality_report_enhanced.json` - 详细分析数据
- `quality_analysis_report_enhanced.md` - 分析报告
- `comprehensive_quality_analysis_enhanced.png` - 综合分析图表
- `recommended_fixes_enhanced.py` - 修复建议代码

### 2. 模型训练

训练RGCN模型：
```bash
python main.py --model RGCN --epochs 100 --lr 0.001 --hidden_dim 128
```

训练GraphSAGE模型：
```bash
python main.py --model GraphSAGE --epochs 100 --lr 0.001 --hidden_dim 128
```

### 3. 特征提取和能耗预测

```bash
python main.py --model RGCN --extract_only
```

## ⚙️ 配置说明

在 `config.py` 中可以配置数据路径：

```python
def get_config(dataset='shenyang'):
    configs = {
        'shenyang': {
            'kg_path': './data/shenyang/kg_without_building_optimized.txt',
            'region_info_path': './data/shenyang/shenyang_region2allinfo.json',
            'train_path': './data/shenyang/shenyang_zl15_train.csv',
            'val_path': './data/shenyang/shenyang_zl15_valid.csv',
            'test_path': './data/shenyang/shenyang_zl15_test.csv',
            'pretrain_path': './data/shenyang/ER_shenhe_TuckER_64.npz'
        }
    }
    return configs[dataset]
```

## 📊 数据格式说明

### 知识图谱文件格式 (*.txt)
```
头实体    关系    尾实体
Region_0    adjacent_to    Region_1
POI_1       located_in     Region_0
District_A  part_of        City_Shenyang
```

### 区域信息文件格式 (*.json)
```json
{
  "Region_0": {
    "energy": 850.5,
    "population": 5000,
    "area": 2.5,
    "building_count": 120
  },
  "Region_1": {
    "energy": 920.3,
    "population": 6200,
    "area": 3.1,
    "building_count": 180
  }
}
```

### 数据集划分文件格式 (*.csv)
```csv
BlockID
Region_0
Region_1
Region_2
```

## 🔧 核心模块说明

### 1. 数据加载模块 (`load_data.py`)
- **Data_CompGCN类**: 知识图谱数据加载和DGL图构建
- **特色功能**: 自动创建示例数据、实体映射、原始图结构构建
- **灵活设计**: 仅构建原始三元组，让模型决定是否需要反向边和自环

### 2. 计算工具模块 (`cal.py`)
- **图操作**: 度归一化、自环添加、图采样
- **复数运算**: 循环相关、旋转操作（支持RotatE等模型）
- **特征处理**: L2/MinMax/Z-score归一化
- **统计分析**: 图结构统计信息计算

### 3. 数据诊断模块 (`data_diagnosis.py`)
- **全面检查**: 数据对齐、质量评估、分布分析
- **知识图谱分析**: 连通性、中心性、实体关系分布
- **异常检测**: 统计异常、孤立森林、聚类异常
- **自动修复**: 生成针对性的修复代码和配置

### 4. 主训练模块 (`main.py`)
- **多模型支持**: RGCN、GraphSAGE
- **灵活配置**: 支持命令行参数和配置文件
- **完整流程**: 训练、特征提取、能耗预测

## 🎯 使用案例

### 案例1: 完整的数据分析和模型训练流程

```bash
# Step 1: 数据质量检查
python data_diagnosis.py

# Step 2: 查看分析报告
cat quality_analysis/quality_analysis_report_enhanced.md

# Step 3: 应用修复建议（如果需要）
python quality_analysis/recommended_fixes_enhanced.py

# Step 4: 训练模型  
python main.py --model RGCN --epochs 50 --batch_size 64

# Step 5: 特征提取和预测
python main.py --model RGCN --extract_only
```

### 案例2: 自定义数据集

```python
# 1. 修改config.py添加新数据集
def get_config(dataset='your_dataset'):
    configs = {
        'your_dataset': {
            'kg_path': './data/your_dataset/kg.txt',
            'region_info_path': './data/your_dataset/regions.json',
            # ... 其他路径
        }
    }

# 2. 运行模型
python main.py --dataset your_dataset --model RGCN
```

## 📈 性能监控

### 训练过程监控
- 训练损失变化趋势
- 最佳模型自动保存
- 参数量统计

### 预测性能评估
- **R²得分**: 决定系数
- **RMSE**: 均方根误差  
- **MAE**: 平均绝对误差
- **MAPE**: 平均绝对百分比误差

## 🐛 常见问题解决

### Q1: 模型导入失败
```bash
# 检查models目录下是否有对应的模型文件
ls models/
# 确保__init__.py文件存在
touch models/__init__.py
```

### Q2: 数据文件不存在
```python
# 使用内置示例数据生成功能
from load_data import create_sample_data_files
create_sample_data_files()
```

### Q3: CUDA内存不足
```bash
# 减小批次大小
python main.py --batch_size 32 --hidden_dim 64
```

### Q4: 数据质量问题
```bash
# 运行数据诊断工具
python data_diagnosis.py
# 查看详细报告并应用修复建议
```

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支: `git checkout -b feature/AmazingFeature`
3. 提交更改: `git commit -m 'Add some AmazingFeature'`
4. 推送到分支: `git push origin feature/AmazingFeature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- DGL团队提供的优秀图学习框架
- PyTorch深度学习框架
- 沈阳市数据提供支持

## 📧 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue到GitHub仓库
- 发送邮件到项目维护者

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**



# 模型图结构配置指南

## 📋 概述

本指南说明不同GNN模型对图结构的需求，包括反向边和自环的添加策略。新的灵活设计让每个模型根据自己的特性来决定图结构，而不是强制添加所有边。

## 🎯 设计原则

1. **按需构建**: 只有模型真正需要时才添加反向边和自环
2. **模型独立**: 每个模型可以有自己的图结构偏好
3. **用户可控**: 用户可以通过参数覆盖默认配置
4. **性能优化**: 避免不必要的边，减少计算开销

## 🔧 模型配置详情

### RGCN (Relational Graph Convolutional Network)

```python
# 默认配置
{
    'add_reverse': True,    # ✅ 需要反向边
    'add_self_loop': True,  # ✅ 需要自环
}
```

**原因分析:**
- **反向边**: RGCN专门处理有向多关系图，反向边让模型能够学习双向关系模式
- **自环**: 帮助节点保持自身信息，避免过度平滑
- **适用场景**: 知识图谱、多关系网络

**使用示例:**
```bash
# 使用默认配置（推荐）
python main.py --model RGCN

# 强制不使用反向边（不推荐）
python main.py --model RGCN --no_reverse
```

### GraphSAGE

```python
# 默认配置
{
    'add_reverse': False,   # ❌ 通常不需要反向边
    'add_self_loop': True,  # ✅ 可选择性需要自环
}
```

**原因分析:**
- **反向边**: GraphSAGE通过邻居采样学习，能够自然处理有向图
- **自环**: 有助于节点信息聚合，但不是必须的
- **适用场景**: 大规模图、归纳学习

**使用示例:**
```bash
# 使用默认配置
python main.py --model GraphSAGE

# 添加反向边（如果需要更强的关系建模）
python main.py --model GraphSAGE --force_reverse
```

### GCN (Graph Convolutional Network)

```python
# 默认配置
{
    'add_reverse': False,   # ❌ 处理无向图
    'add_self_loop': True,  # ✅ 通常需要自环
}
```

**原因分析:**
- **反向边**: 标准GCN假设无向图，添加反向边可能造成重复计算
- **自环**: GCN公式中通常需要自环来避免度为0的节点问题
- **适用场景**: 社交网络、无向图

### GAT (Graph Attention Network)

```python
# 默认配置  
{
    'add_reverse': False,   # ❌ 注意力机制处理方向性
    'add_self_loop': True,  # ✅ 有助于注意力计算
}
```

**原因分析:**
- **反向边**: GAT通过注意力权重自动学习边的重要性
- **自环**: 允许节点关注自身，提高表达能力
- **适用场景**: 需要可解释性的场景

## 🛠️ 自定义配置

### 在代码中自定义模型配置

```python
# 在main.py中修改get_model_graph_config函数
def get_model_graph_config(model_name):
    model_configs = {
        'YourCustomModel': {
            'add_reverse': True,    # 根据需要设置
            'add_self_loop': False, # 根据需要设置
            'description': '你的模型描述'
        }
    }
    return model_configs.get(model_name, default_config)
```

### 命令行参数覆盖

```bash
# 强制添加反向边
python main.py --model YourModel --force_reverse

# 强制不添加自环
python main.py --model YourModel --no_self_loop

# 同时设置两个参数
python main.py --model YourModel --force_reverse --no_self_loop
```

### 在模型类中动态调整

```python
class YourCustomModel(nn.Module):
    def __init__(self, data_loader, **kwargs):
        super().__init__()
        
        # 根据模型需求获取图
        self.graph = data_loader.get_graph_for_model(
            model_name="YourCustomModel",
            add_reverse=True,  # 你的模型需求
            add_self_loop=True
        )
```

## 📊 性能对比

| 模型 | 反向边 | 自环 | 边数变化 | 内存使用 | 训练速度 |
|------|--------|------|----------|----------|----------|
| RGCN | ✅ | ✅ | ~3x | 高 | 较慢 |
| GraphSAGE | ❌ | ✅ | ~1.5x | 中 | 快 |
| GCN | ❌ | ✅ | ~1.5x | 低 | 最快 |
| GAT | ❌ | ✅ | ~1.5x | 中 | 中等 |

## 🔍 如何选择配置

### 1. 根据数据特征选择

```python
# 有向多关系知识图谱
recommended_models = ["RGCN"]
config = {'add_reverse': True, 'add_self_loop': True}

# 大规模无向社交网络  
recommended_models = ["GraphSAGE", "GCN"]
config = {'add_reverse': False, 'add_self_loop': True}

# 需要可解释性的网络
recommended_models = ["GAT"]
config = {'add_reverse': False, 'add_self_loop': True}
```

### 2. 根据计算资源选择

```python
# 资源充足，追求最佳性能
use_config = {'add_reverse': True, 'add_self_loop': True}

# 资源有限，需要快速训练
use_config = {'add_reverse': False, 'add_self_loop': True}

# 极度资源受限
use_config = {'add_reverse': False, 'add_self_loop': False}
```

### 3. 实验性比较

```bash
# 测试不同配置的性能
python main.py --model RGCN --extract_only  # 默认配置
python main.py --model RGCN --no_reverse --extract_only  # 无反向边
python main.py --model RGCN --no_self_loop --extract_only  # 无自环
```

## 🚀 最佳实践

### 1. 新模型开发流程

```python
# Step 1: 实现基础模型（假设最简图结构）
class NewModel(nn.Module):
    def __init__(self, graph, **kwargs):
        # 基于原始图实现
        pass

# Step 2: 测试不同图配置
for add_reverse in [True, False]:
    for add_self_loop in [True, False]:
        graph = data_loader.get_graph_for_model(
            "NewModel", add_reverse, add_self_loop
        )
        # 测试性能

# Step 3: 确定最佳配置并写入get_model_graph_config()
```

### 2. 性能调优建议

```python
# 如果内存不足
config = {'add_reverse': False, 'add_self_loop': True}

# 如果训练太慢
config = {'add_reverse': False, 'add_self_loop': False}

# 如果精度不够
config = {'add_reverse': True, 'add_self_loop': True}
```

### 3. 调试和分析

```python
# 分析图结构影响
data_loader = Data_CompGCN(kg_path, dataset)

print("原始图:", data_loader.g.num_edges())

graph_with_reverse = data_loader.get_graph_for_model(
    "test", add_reverse=True, add_self_loop=False
)
print("添加反向边:", graph_with_reverse.num_edges())

graph_with_self_loop = data_loader.get_graph_for_model(
    "test", add_reverse=False, add_self_loop=True  
)
print("添加自环:", graph_with_self_loop.num_edges())
```

## ⚠️ 注意事项

1. **内存使用**: 反向边会使边数翻倍，注意内存消耗
2. **训练时间**: 更多的边意味着更长的训练时间
3. **模型假设**: 确保图结构符合模型的理论假设
4. **数据特征**: 考虑原始数据的有向/无向特性
5. **下游任务**: 不同任务可能需要不同的图结构

## 📚 参考文献

- RGCN: Schlichtkrull et al. "Modeling Relational Data with Graph Convolutional Networks"
- GraphSAGE: Hamilton et al. "Inductive Representation Learning on Large Graphs"  
- GCN: Kipf & Welling "Semi-Supervised Classification with Graph Convolutional Networks"
- GAT: Veličković et al. "Graph Attention Networks"