# 两版知识图谱三元组详细罗列

## 第一部分：无建筑物版本三元组 (enhanced_kg_generatorV3.py)

### 1. 空间关系三元组

#### 1.1 borderBy（边界复合关系）
```
(Region_A, borderBy, Region_B)
(Region_B, borderBy, Region_A)
```
**生成条件**：
- 区域边界相接：`geometry1.touches(geometry2)`
- 缓冲区内：距离在300-800米之间
- 空间重叠：缓冲区重叠比例 > 0.1

**示例**：
```
(Region_001, borderBy, Region_002)
(Region_002, borderBy, Region_001)
(Region_003, borderBy, Region_005)
```

#### 1.2 nearBy（近距离复合关系）
```
(Region_A, nearBy, Region_B)
(Region_B, nearBy, Region_A)
```
**生成条件**：
- 近距离：质心距离 < 300米
- 可达性：质心距离在200-1500米之间

**示例**：
```
(Region_001, nearBy, Region_004)
(Region_002, nearBy, Region_003)
```

#### 1.3 locateAt（POI定位关系）
```
(POI_ID, locateAt, Region_ID)
```
**生成条件**：POI在街区几何范围内

**示例**：
```
(POI_12345, locateAt, Region_001)
(POI_12346, locateAt, Region_002)
(POI_12347, locateAt, Region_001)
```

### 2. 功能相似性关系三元组

#### 2.1 similarFunction（功能相似复合关系）
```
(Region_A, similarFunction, Region_B)
(Region_B, similarFunction, Region_A)
```
**生成条件**（满足任一即可）：
- 功能分布相似：余弦相似度 ≥ 0.4
- POI数量相似：数量比值 ≥ 0.6
- 类别多样性相似：类别数比值 ≥ 0.7
- 主导功能相同：最多POI类别相同
- 功能互补：存在预定义的互补关系

**示例**：
```
(Region_001, similarFunction, Region_003)
(Region_002, similarFunction, Region_005)
```

#### 2.2 highConvenience（便利性关系）
```
(Region_A, highConvenience, Region_B)
(Region_B, highConvenience, Region_A)
```
**生成条件**：
- 两个区域都满足便利性标准
- 区域间距离 < 1000米

**便利性标准**：
- POI类别数 ≥ 7种
- POI总数 ≥ 20个
- 包含全部4种基本服务（餐饮、购物、生活服务、交通设施）
- 便利性得分 ≥ 12分

**示例**：
```
(Region_002, highConvenience, Region_004)
(Region_003, highConvenience, Region_006)
```

### 3. 移动性关系三元组

#### 3.1 flowTransition（流动复合关系）
```
(Region_A, flowTransition, Region_B)
```
**生成条件**（满足任一即可）：
- 实际流动：基于签到数据，时间差 < 2小时
- 模拟流动：基于预定义流动模式，距离 < 1000米
- 吸引流动：向高吸引力区域，距离 < 2000米

**流动模式**：
- 交通设施服务 → 商务住宅
- 餐饮服务 → 购物服务
- 教育文化服务 → 餐饮服务
- 医疗保健服务 → 生活服务
- 体育休闲服务 → 餐饮服务
- 商务住宅 → 生活服务

**示例**：
```
(Region_001, flowTransition, Region_003)
(Region_002, flowTransition, Region_001)
(Region_004, flowTransition, Region_002)
```

### 4. 分类关系三元组

#### 4.1 cateOf（POI类别归属）
```
(POI_ID, cateOf, Category_ID)
```
**生成条件**：每个POI对应其主要类别

**示例**：
```
(POI_12345, cateOf, Cate_0)    # 餐饮服务
(POI_12346, cateOf, Cate_1)    # 购物服务
(POI_12347, cateOf, Cate_2)    # 生活服务
```

### 5. 服务关系三元组

#### 5.1 provideService（商圈服务关系）
```
(BusinessCircle_ID, provideService, Region_ID)
```
**生成条件**：商圈几何范围包含或相交街区

**示例**：
```
(BC_1, provideService, Region_001)
(BC_2, provideService, Region_002)
(BC_1, provideService, Region_003)
```

---

## 第二部分：有建筑物版本三元组 (optimized_kg_generator.py)

### A. 层次归属关系三元组 (3种)

#### A.1 belongsToLand（建筑物归属地块）
```
(Building_ID, belongsToLand, Land_ID)
```
**示例**：
```
(Building_0001, belongsToLand, Land_001)
(Building_0002, belongsToLand, Land_001)
(Building_0003, belongsToLand, Land_002)
```

#### A.2 belongsToRegion（地块归属街区）
```
(Land_ID, belongsToRegion, Region_ID)
```
**示例**：
```
(Land_001, belongsToRegion, Region_001)
(Land_002, belongsToRegion, Region_001)
(Land_003, belongsToRegion, Region_002)
```

#### A.3 locateAt（POI定位街区）
```
(POI_ID, locateAt, Region_ID)
```
**示例**：
```
(POI_12345, locateAt, Region_001)
(POI_12346, locateAt, Region_002)
```

### B. 属性关联关系三元组 (6种)

#### B.1 hasFunction（建筑物功能属性）
```
(Building_ID, hasFunction, Function_ID)
```
**功能类型**：
- Func_Residential（住宅）
- Func_Commercial（商业）
- Func_Office（办公）
- Func_Industrial（工业）
- Func_Public（公共服务）
- Func_Education（教育）
- Func_Medical（医疗）
- Func_Cultural（文化）
- Func_Sports（体育）
- Func_Transport（交通）
- Func_Other（其他）

**示例**：
```
(Building_0001, hasFunction, Func_Residential)
(Building_0002, hasFunction, Func_Commercial)
(Building_0003, hasFunction, Func_Office)
```

#### B.2 hasMorphology（地块形态属性）
```
(Land_ID, hasMorphology, Morphology_ID)
```
**形态类型**（基于Spacematrix）：
- Morph_LowRiseLowDensity
- Morph_LowRiseMidDensity
- Morph_LowRiseHighDensity
- Morph_MidRiseLowDensity
- Morph_MidRiseMidDensity
- Morph_MidRiseHighDensity
- Morph_HighRiseLowDensity
- Morph_HighRiseMidDensity
- Morph_HighRiseHighDensity
- Morph_SuperHighRise
- Morph_Vacant

**示例**：
```
(Land_001, hasMorphology, Morph_LowRiseMidDensity)
(Land_002, hasMorphology, Morph_HighRiseHighDensity)
(Land_003, hasMorphology, Morph_MidRiseMidDensity)
```

#### B.3 hasLandUse（地块土地利用属性）
```
(Land_ID, hasLandUse, LandUse_ID)
```
**土地利用类型**：
- LandUse_Residential（居住用地）
- LandUse_Commercial（商业用地）
- LandUse_Industrial（工业用地）
- LandUse_Public（公共设施用地）
- LandUse_Green（绿地）
- LandUse_Transport（交通用地）
- LandUse_Water（水域）
- LandUse_Agricultural（农业用地）
- LandUse_Other（其他）

**示例**：
```
(Land_001, hasLandUse, LandUse_Residential)
(Land_002, hasLandUse, LandUse_Commercial)
(Land_003, hasLandUse, LandUse_Public)
```

#### B.4 hasDominantFunction（街区主导功能）
```
(Region_ID, hasDominantFunction, RegionFunc_ID)
```
**街区功能类型**：
- RegionFunc_Commercial（商业主导）
- RegionFunc_Residential（居住主导）
- RegionFunc_Office（办公主导）
- RegionFunc_Education（教育主导）
- RegionFunc_Mixed（混合功能）

**示例**：
```
(Region_001, hasDominantFunction, RegionFunc_Commercial)
(Region_002, hasDominantFunction, RegionFunc_Residential)
(Region_003, hasDominantFunction, RegionFunc_Mixed)
```

#### B.5 cateOf（POI类别归属）
```
(POI_ID, cateOf, Category_ID)
```
**示例**：
```
(POI_12345, cateOf, Cate_0)
(POI_12346, cateOf, Cate_1)
```

#### B.6 hasPhysicalAttribute（建筑物物理属性）
```
(Building_ID, hasPhysicalAttribute, PhysicalAttr_ID)
```
**物理属性类型**（27种组合）：
- 面积：Small/Medium/Large
- 高度：Low/Mid/High
- 年代：Old/Mid/New

**所有可能的组合**：
- PhysicalAttr_SmallLowOld
- PhysicalAttr_SmallLowMid
- PhysicalAttr_SmallLowNew
- PhysicalAttr_SmallMidOld
- PhysicalAttr_SmallMidMid
- PhysicalAttr_SmallMidNew
- PhysicalAttr_SmallHighOld
- PhysicalAttr_SmallHighMid
- PhysicalAttr_SmallHighNew
- PhysicalAttr_MediumLowOld
- ... (共27种)
- PhysicalAttr_LargeHighNew

**示例**：
```
(Building_0001, hasPhysicalAttribute, PhysicalAttr_MediumMidMid)
(Building_0002, hasPhysicalAttribute, PhysicalAttr_LargeHighNew)
(Building_0003, hasPhysicalAttribute, PhysicalAttr_SmallLowOld)
```

### C. 同层空间关系三元组 (6种)

#### C.1 connectedTo（建筑物间连接）
```
(Building_A, connectedTo, Building_B)
(Building_B, connectedTo, Building_A)
```
**生成条件**：建筑物间距离 < 150米

**示例**：
```
(Building_0001, connectedTo, Building_0002)
(Building_0002, connectedTo, Building_0003)
```

#### C.2 adjacentTo（地块邻接关系）
```
(Land_A, adjacentTo, Land_B)
(Land_B, adjacentTo, Land_A)
```
**生成条件**：地块几何边界相接

**示例**：
```
(Land_001, adjacentTo, Land_002)
(Land_002, adjacentTo, Land_003)
```

#### C.3 borderBy（街区边界相接）
```
(Region_A, borderBy, Region_B)
(Region_B, borderBy, Region_A)
```
**生成条件**：街区边界相接且接触长度 > 10米

**示例**：
```
(Region_001, borderBy, Region_002)
(Region_002, borderBy, Region_003)
```

#### C.4 nearBy（街区近距离关系）
```
(Region_A, nearBy, Region_B)
(Region_B, nearBy, Region_A)
```
**生成条件**：街区质心距离 ≤ 1000米且不相接

**示例**：
```
(Region_001, nearBy, Region_004)
(Region_002, nearBy, Region_005)
```

#### C.5 similarMorphology（地块形态相似）
```
(Land_A, similarMorphology, Land_B)
(Land_B, similarMorphology, Land_A)
```
**生成条件**：
- 相同形态类型
- 距离 < 300米

**示例**：
```
(Land_001, similarMorphology, Land_003)
(Land_002, similarMorphology, Land_005)
```

#### C.6 similarFunction（建筑物功能相似）
```
(Building_A, similarFunction, Building_B)
(Building_B, similarFunction, Building_A)
```
**生成条件**：
- 相同功能类型
- 距离 < 200米

**示例**：
```
(Building_0001, similarFunction, Building_0003)
(Building_0002, similarFunction, Building_0004)
```

### D. 跨层功能关系三元组 (4种)

#### D.1 functionalSimilarity（街区功能相似性）
```
(Region_A, functionalSimilarity, Region_B)
(Region_B, functionalSimilarity, Region_A)
```
**生成条件**：POI分布余弦相似度 ≥ 0.35

**示例**：
```
(Region_001, functionalSimilarity, Region_003)
(Region_002, functionalSimilarity, Region_004)
```

#### D.2 highConvenience（街区便利性关联）
```
(Region_A, highConvenience, Region_B)
(Region_B, highConvenience, Region_A)
```
**生成条件**：
- 两个区域都满足便利性标准
- 距离 < 1200米

**便利性标准**（相对宽松）：
- POI类别数 ≥ 6种
- POI总数 ≥ 15个
- 包含至少3种基本服务类别
- 便利性得分 ≥ 10分

**示例**：
```
(Region_002, highConvenience, Region_005)
(Region_003, highConvenience, Region_007)
```

#### D.3 functionalComplementarity（街区功能互补）
```
(Region_A, functionalComplementarity, Region_B)
(Region_B, functionalComplementarity, Region_A)
```
**生成条件**：
- 基于预定义互补关系
- 距离 < 1500米

**互补关系**：
- 餐饮服务 ↔ 购物服务
- 生活服务 ↔ 体育休闲服务
- 医疗保健服务 ↔ 生活服务
- 教育文化服务 ↔ 体育休闲服务
- 交通设施服务 ↔ 商务住宅
- 办公 ↔ 餐饮服务
- 住宅 ↔ 生活服务
- 商业 ↔ 交通设施服务

**示例**：
```
(Region_001, functionalComplementarity, Region_003)
(Region_002, functionalComplementarity, Region_004)
```

#### D.4 densityInfluence（密度影响关系）
```
(HighDensityRegion, densityInfluence, LowDensityRegion)
```
**生成条件**：
- 高密度区域对低密度区域的影响
- 距离 < 2000米
- 密度比值 ≥ 1.5

**示例**：
```
(Region_001, densityInfluence, Region_004)
(Region_002, densityInfluence, Region_006)
```

### E. 移动性关系三元组 (1种)

#### E.1 flowTransition（街区间人流移动）
```
(Region_A, flowTransition, Region_B)
```
**生成条件**：基于流动模式，距离 < 1500米

**示例**：
```
(Region_001, flowTransition, Region_003)
(Region_002, flowTransition, Region_001)
```

### F. 服务关系三元组 (1种)

#### F.1 provideService（商圈服务街区）
```
(BusinessCircle_ID, provideService, Region_ID)
```
**生成条件**：
- 商圈几何范围包含或相交街区，或
- 商圈质心距离街区质心 ≤ 3000米

**示例**：
```
(BC_1, provideService, Region_001)
(BC_2, provideService, Region_002)
```

---

## 第三部分：三元组数量对比

### 无建筑物版本预期三元组数量
| 关系类型 | 预期数量范围 | 说明 |
|---------|-------------|------|
| locateAt | 10,000-50,000 | 取决于POI数量 |
| borderBy | 100-500 | 取决于街区数量和邻接关系 |
| nearBy | 200-1,000 | 取决于街区分布密度 |
| similarFunction | 50-300 | 取决于功能相似度阈值 |
| highConvenience | 10-100 | 取决于便利性街区数量 |
| flowTransition | 100-1,000 | 取决于流动模式和距离 |
| cateOf | 10,000-50,000 | 等于POI数量 |
| provideService | 50-500 | 取决于商圈覆盖范围 |
| **总计** | **~20,000-100,000** | 主要由POI相关关系贡献 |

### 有建筑物版本预期三元组数量
| 关系类型 | 预期数量范围 | 说明 |
|---------|-------------|------|
| belongsToLand | 50,000-200,000 | 取决于建筑物数量 |
| belongsToRegion | 1,000-5,000 | 取决于地块数量 |
| locateAt | 10,000-50,000 | 取决于POI数量 |
| hasFunction | 50,000-200,000 | 等于建筑物数量 |
| hasMorphology | 1,000-5,000 | 等于地块数量 |
| hasLandUse | 1,000-5,000 | 等于地块数量 |
| hasDominantFunction | 100-1,000 | 等于街区数量 |
| cateOf | 10,000-50,000 | 等于POI数量 |
| hasPhysicalAttribute | 50,000-200,000 | 等于建筑物数量 |
| connectedTo | 10,000-100,000 | 取决于建筑物连接密度 |
| adjacentTo | 2,000-10,000 | 取决于地块邻接关系 |
| borderBy | 200-1,000 | 取决于街区邻接关系 |
| nearBy | 200-1,000 | 取决于街区分布 |
| similarMorphology | 1,000-5,000 | 取决于形态分布 |
| similarFunction | 5,000-20,000 | 取决于建筑功能分布 |
| functionalSimilarity | 50-300 | 取决于街区功能相似度 |
| highConvenience | 10-100 | 取决于便利性街区数量 |
| functionalComplementarity | 100-500 | 取决于互补关系 |
| densityInfluence | 50-200 | 取决于密度差异 |
| flowTransition | 100-1,000 | 取决于流动模式 |
| provideService | 50-500 | 取决于商圈覆盖范围 |
| **总计** | **~200,000-900,000** | 显著高于无建筑物版本 |

**结论**：有建筑物版本的三元组数量约为无建筑物版本的5-10倍，这主要由于增加了建筑物和地块层次的关系。
