"""
优化版无建筑物知识图谱生成器
关系类型合并优化，增强进度条显示

关系类型合并后：
1. 空间关系 (2种)：
   - borderBy: 区域边界相接 + 缓冲区内 + 空间重叠
   - nearBy: 区域距离很近 + 区域间可达

2. 功能相似性关系 (2种)：
   - similarFunction: 功能分布相似 + POI数量相似 + 类别多样性相似 + 主导功能相同 + 功能互补
   - highConvenience: 街区功能便利性强 (种类齐全)

3. 移动性关系 (1种)：
   - flowTransition: 实际人员流动 + 模拟流动 + 吸引流动

4. 分类关系 (1种)：
   - cateOf: POI属于类别
   - [已移除] belongTo: POI属于商圈

5. 服务关系 (1种)：
   - provideService: 商圈服务区域
"""

import os
import sys
import pandas as pd
import geopandas as gpd
import numpy as np
from shapely.geometry import Point
from scipy.spatial.distance import cosine
import itertools
from tqdm import tqdm
import time

# ==================== 配置部分 ====================

# 数据路径配置
DATA_PATHS = {
    "l4_shp_path": r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L4.shp",
    "poi_path": r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
    "bc_path": r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
    "checkin_path": r"D:\研二\能耗估算\1-沈阳\1-数据\6-微博数据\weibo1.csv",
}

# 输出路径配置 - 更改为新的指定路径
OUTPUT_BASE_PATH = r"D:\研二\能耗估算\666-模型对比项目\V10-KG\无建筑物\output"
OUTPUT_PATHS = {
    "kg_without_building": os.path.join(OUTPUT_BASE_PATH, "kg_without_building_optimized.txt"),
    "category_mapping": os.path.join(OUTPUT_BASE_PATH, "category_mapping.csv"),
    "relation_stats": os.path.join(OUTPUT_BASE_PATH, "relation_statistics.csv"),
}

# 优化参数配置
PARAMS = {
    # === 空间关系参数 ===
    "nearby_distance": 300,        # nearBy关系距离阈值(米)
    "buffer_distance": 800,        # withinBuffer关系距离阈值(米)
    "overlap_threshold": 0.1,      # spatialOverlap重叠比例阈值
    "accessibility_min": 200,      # accessibleBy最小距离(米)
    "accessibility_max": 1500,     # accessibleBy最大距离(米)
    
    # === 相似性参数 ===
    "function_similarity_threshold": 0.4,    # 功能相似度阈值 - 降低增加连接
    "poi_count_similarity_threshold": 0.6,   # POI数量相似度阈值
    "category_diversity_threshold": 0.7,     # 类别多样性相似度阈值
    
    # === 便利性参数 - 更严格的条件 ===
    "convenience_min_categories": 7,         # 便利性最少POI类别数（提高到7）
    "convenience_min_pois": 20,              # 便利性最少POI总数（提高到20）
    "convenience_essential_categories": [     # 必需的基本服务类别（必须全覆盖）
        "餐饮服务", "购物服务", "生活服务", "交通设施服务"
    ],
    "convenience_score_threshold": 12,       # 便利性得分门槛（提高到12分）
    "convenience_distance_threshold": 1000,  # 便利性街区间最大距离（米）
    
    # === 移动性参数 ===
    "flow_time_threshold": 7200,             # 流动时间阈值(秒) - 2小时  
    "flow_distance_threshold": 1000,         # 模拟流动距离阈值(米)
    "attraction_distance_threshold": 2000,   # 吸引流动距离阈值(米)
    
    # === 坐标系统 ===
    "crs": "EPSG:4326",           # WGS84坐标系
    "utm_crs": "EPSG:32651",      # UTM 51N，适合沈阳地区
    
    # === ID前缀 ===
    "region_prefix": "Region_",
    "poi_prefix": "POI_",
    "category_prefix": "Cate_",
    "bc_prefix": "BC_",
}

# 字段名映射
FIELD_MAPPING = {
    "l4": {
        "id_field": "BlockID",
        "geometry_field": "geometry",
    },
    "poi": {
        "name_field": "name",
        "category_field": "main_cat",
        "subcategory_field": "sub_cat",
        "geometry_field": "geometry",
    },
    "bc": {
        "id_field": "OBJECTID",
        "name_field": "Name_CHN",
        "geometry_field": "geometry",
    },
    "checkin": {
        "user_field": "账号",
        "time_field": "发布时间",
        "lon_field": "经度",
        "lat_field": "纬度",
    },
}

# 功能互补关系配置
FUNCTIONAL_COMPLEMENTS = [
    ("餐饮服务", "购物服务"),
    ("生活服务", "体育休闲服务"),
    ("医疗保健服务", "生活服务"),
    ("教育文化服务", "体育休闲服务"),
    ("交通设施服务", "商务住宅"),
]

# 模拟流动模式配置
FLOW_PATTERNS = [
    ("交通设施服务", "商务住宅"),
    ("餐饮服务", "购物服务"),
    ("教育文化服务", "餐饮服务"),
    ("医疗保健服务", "生活服务"),
    ("体育休闲服务", "餐饮服务"),
    ("商务住宅", "生活服务"),
]

# 高吸引力POI类型
HIGH_ATTRACTION_CATEGORIES = [
    "购物服务", "餐饮服务", "体育休闲服务", 
    "旅游景点", "教育文化服务", "交通设施服务"
]

# ==================== 优化的工具函数 ====================

def ensure_dir(file_path):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    # 如果directory为空字符串，说明文件在当前目录，不需要创建目录
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        print(f"✅ 创建输出目录: {directory}")

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def show_progress(iterable, desc, total=None, show_details=True):
    """优化的进度条显示 - 黑白配色，显示详细信息"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    
    # 黑白配色的进度条
    bar_format = '{l_bar}{bar:30}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]'
    if show_details:
        bar_format = '{desc}: {percentage:3.0f}%|{bar:30}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
    
    return tqdm(iterable, desc=desc, total=total, 
               bar_format=bar_format,
               colour=None,  # 使用默认黑白
               ascii=True,   # 使用ASCII字符
               ncols=100)

def show_detailed_progress(iterable, desc, total=None):
    """显示详细进度的进度条 - 适用于耗时长但项目少的情况"""
    if total is None:
        total = len(iterable) if hasattr(iterable, '__len__') else None
    
    pbar = tqdm(total=total, desc=desc, 
                bar_format='{desc}: {percentage:3.0f}%|{bar:40}| {n}/{total} [{elapsed}<{remaining}]',
                colour=None, ascii=True, ncols=120)
    
    for i, item in enumerate(iterable):
        # 更新进度条并显示当前处理项目的详细信息
        if hasattr(item, '__str__') and len(str(item)) < 50:
            pbar.set_postfix_str(f"处理: {str(item)[:47]}...")
        else:
            pbar.set_postfix_str(f"第{i+1}项")
        
        yield item
        pbar.update(1)
        
    pbar.close()

# ==================== 数据加载与预处理 ====================

def load_data():
    """加载所有数据文件"""
    print_section("数据加载")
    
    print("正在加载数据文件...")
    
    data_files = [
        ("L4街区数据", DATA_PATHS["l4_shp_path"]),
        ("POI数据", DATA_PATHS["poi_path"]),
        ("商圈数据", DATA_PATHS["bc_path"]),
        ("签到数据", DATA_PATHS["checkin_path"])
    ]
    
    results = []
    
    # 使用详细进度条显示加载过程
    for desc, path in show_detailed_progress(data_files, "加载数据文件"):
        try:
            start_time = time.time()
            if path.endswith('.shp'):
                data = gpd.read_file(path)
            else:
                data = pd.read_csv(path)
            
            load_time = time.time() - start_time
            results.append(data)
            tqdm.write(f"✅ {desc}: {len(data):,} 条记录 (耗时: {load_time:.1f}s)")
            
        except Exception as e:
            tqdm.write(f"❌ {desc} 加载失败: {e}")
            results.append(pd.DataFrame())
    
    return tuple(results)

def preprocess_data(l4_gdf, poi_gdf, bc_gdf, checkin_df):
    """数据预处理：统一坐标系，添加ID，空间关联"""
    print_section("数据预处理")
    
    # 1. 统一坐标系
    print("统一坐标系到UTM...")
    utm_crs = PARAMS["utm_crs"]
    
    conversion_tasks = [
        ("L4区域", l4_gdf),
        ("POI数据", poi_gdf), 
        ("商圈数据", bc_gdf)
    ]
    
    converted_results = []
    for desc, data in show_detailed_progress(conversion_tasks, "坐标系转换"):
        converted_data = data.to_crs(utm_crs)
        converted_results.append(converted_data)
        tqdm.write(f"✅ {desc}: 转换完成")
    
    l4_gdf, poi_gdf, bc_gdf = converted_results
    
    # 2. 添加统一ID
    print("生成统一ID...")
    
    # L4区域ID
    l4_gdf.columns = l4_gdf.columns.str.lower()
    id_field = FIELD_MAPPING["l4"]["id_field"].lower()
    
    if id_field in l4_gdf.columns:
        l4_gdf["region_id"] = l4_gdf[id_field].apply(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
        print(f"✅ 使用字段 '{id_field}' 生成区域ID")
    else:
        l4_gdf["region_id"] = l4_gdf.index.map(
            lambda x: f"{PARAMS['region_prefix']}{x}"
        )
        print(f"⚠️ 字段 '{id_field}' 不存在，使用索引生成区域ID")
    
    # POI ID和类别ID
    poi_gdf["poi_id"] = poi_gdf.index.map(lambda x: f"{PARAMS['poi_prefix']}{x}")
    
    unique_cats = poi_gdf['main_cat'].dropna().unique()
    category_mapping = {cat: f"{PARAMS['category_prefix']}{i}" 
                       for i, cat in enumerate(unique_cats)}
    poi_gdf["category_id"] = poi_gdf['main_cat'].map(
        lambda x: category_mapping.get(x, f"{PARAMS['category_prefix']}_None") 
        if pd.notna(x) else f"{PARAMS['category_prefix']}_None"
    )
    
    # 保存类别映射
    ensure_dir(OUTPUT_PATHS["category_mapping"])
    pd.DataFrame({
        'main_cat': list(category_mapping.keys()),
        'category_id': list(category_mapping.values())
    }).to_csv(OUTPUT_PATHS["category_mapping"], index=False, encoding='utf-8')
    
    # 商圈ID
    bc_gdf["bc_id"] = bc_gdf["OBJECTID"].apply(
        lambda x: f"{PARAMS['bc_prefix']}{int(x)}" if pd.notna(x) else "BC_0"
    )
    
    # 3. 处理签到数据
    print("处理签到数据...")
    try:
        checkin_df["经度"] = pd.to_numeric(checkin_df["经度"], errors='coerce')
        checkin_df["纬度"] = pd.to_numeric(checkin_df["纬度"], errors='coerce')
        
        valid_coords = (
            (checkin_df["经度"] >= 120.0) & (checkin_df["经度"] <= 130.0) &
            (checkin_df["纬度"] >= 38.0) & (checkin_df["纬度"] <= 45.0)
        )
        checkin_df = checkin_df[valid_coords]
        
        geometry = [Point(xy) for xy in zip(checkin_df["经度"], checkin_df["纬度"])]
        checkin_gdf = gpd.GeoDataFrame(checkin_df, geometry=geometry, crs="EPSG:4326")
        checkin_gdf = checkin_gdf.to_crs(utm_crs)
        
        print(f"✅ 有效签到数据: {len(checkin_gdf):,} 条")
    except Exception as e:
        print(f"⚠️ 签到数据处理失败: {e}")
        checkin_gdf = gpd.GeoDataFrame()
    
    # 4. 空间关联
    print("执行空间关联...")
    
    spatial_tasks = [
        ("POI与区域关联", lambda: gpd.sjoin(poi_gdf, l4_gdf[["geometry", "region_id"]], 
                                          how='left', predicate='within')),
        ("POI与商圈关联", lambda: gpd.sjoin(poi_gdf, bc_gdf[["geometry", "bc_id"]], 
                                          how='left', predicate='within'))
    ]
    
    # POI与区域关联
    poi_gdf = gpd.sjoin(poi_gdf, l4_gdf[["geometry", "region_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "street_index"}, inplace=True)
    
    # POI与商圈关联
    poi_gdf = gpd.sjoin(poi_gdf, bc_gdf[["geometry", "bc_id"]], 
                       how='left', predicate='within')
    poi_gdf.rename(columns={"index_right": "bc_index"}, inplace=True)
    
    # 签到与区域关联
    if not checkin_gdf.empty:
        print("  签到与区域空间关联...")
        try:
            checkin_with_region = gpd.sjoin(checkin_gdf, l4_gdf[["geometry", "region_id"]], 
                                          how='left', predicate='within')
            checkin_gdf["region_id"] = checkin_with_region["region_id"]
            matched = checkin_gdf["region_id"].notna().sum()
            print(f"✅ 签到匹配结果: {matched:,}/{len(checkin_gdf):,} 条匹配到区域")
        except Exception as e:
            print(f"⚠️ 签到空间关联失败: {e}")
    
    # 5. 数据过滤
    print("根据L4边界过滤数据...")
    l4_boundary = l4_gdf.unary_union
    
    before_poi = len(poi_gdf)
    before_bc = len(bc_gdf)
    
    poi_gdf = poi_gdf[poi_gdf.intersects(l4_boundary)]
    bc_gdf = bc_gdf[bc_gdf.intersects(l4_boundary)]
    
    print(f"✅ POI数据: {len(poi_gdf):,}/{before_poi:,} ({len(poi_gdf)/before_poi*100:.1f}%)")
    print(f"✅ 商圈数据: {len(bc_gdf):,}/{before_bc:,} ({len(bc_gdf)/before_bc*100:.1f}%)")
    
    return l4_gdf, poi_gdf, bc_gdf, checkin_gdf

# ==================== 合并后的关系生成函数 ====================

def generate_spatial_relations(l4_gdf, poi_gdf):
    """
    生成合并后的空间关系 (2种)
    
    关系说明：
    - borderBy: 区域边界相接 + 缓冲区内 + 空间重叠
    - nearBy: 区域距离很近 + 区域间可达
    """
    print_section("生成空间关系")
    triples = []
    
    # 计算区域中心点
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    print("1. borderBy - 边界相接/缓冲区/重叠关系...")
    count = 0
    region_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_gdf.itertuples()) 
                   for j, row2 in enumerate(l4_gdf.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(region_pairs, "边界/缓冲区/重叠检测"):
        # 原borderBy: 边界相接
        if row1.geometry.touches(row2.geometry):
            triples.append((row1.region_id, "borderBy", row2.region_id))
            count += 1
            continue
            
        # 原spatialOverlap: 空间重叠
        buffer1 = row1.geometry.buffer(100)
        buffer2 = row2.geometry.buffer(100)
        intersection = buffer1.intersection(buffer2)
        if not intersection.is_empty:
            overlap_ratio = intersection.area / min(buffer1.area, buffer2.area)
            if overlap_ratio > PARAMS["overlap_threshold"]:
                triples.append((row1.region_id, "borderBy", row2.region_id))
                count += 1
                continue
        
        # 原withinBuffer: 缓冲区内
        centroid1 = row1.geometry.centroid  
        centroid2 = row2.geometry.centroid
        distance = centroid1.distance(centroid2)
        if PARAMS["nearby_distance"] < distance < PARAMS["buffer_distance"]:
            triples.append((row1.region_id, "borderBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 borderBy 三元组")
    
    print("2. nearBy - 近距离/可达关系...")
    count = 0
    centroid_pairs = [(i, j, row1, row2) for i, row1 in enumerate(l4_centroids.itertuples()) 
                     for j, row2 in enumerate(l4_centroids.itertuples()) if i < j]
    
    for i, j, row1, row2 in show_progress(centroid_pairs, "近距离/可达性计算"):
        distance = row1.geometry.distance(row2.geometry)
        
        # 原nearBy: 近距离
        if distance < PARAMS["nearby_distance"]:
            triples.append((row1.region_id, "nearBy", row2.region_id))
            count += 1
        # 原accessibleBy: 可达性
        elif PARAMS["accessibility_min"] < distance < PARAMS["accessibility_max"]:
            triples.append((row1.region_id, "nearBy", row2.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 nearBy 三元组")
    
    print("3. locateAt - POI位置关系...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI位置"):
        if pd.notna(row.region_id):
            triples.append((row.poi_id, "locateAt", row.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 locateAt 三元组")
    
    print(f"✅ 空间关系总计: {len(triples):,} 个三元组")
    return triples

def calculate_poi_distribution(l4_gdf, poi_gdf):
    """计算每个区域的POI类别分布向量"""
    all_categories = poi_gdf["main_cat"].dropna().unique()
    category_to_idx = {cat: i for i, cat in enumerate(all_categories)}
    
    distributions = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        distribution = np.zeros(len(all_categories))
        
        if not region_pois.empty:
            for _, poi in region_pois.iterrows():
                category = poi.main_cat
                if pd.notna(category) and category in category_to_idx:
                    distribution[category_to_idx[category]] += 1
            
            if distribution.sum() > 0:
                distribution = distribution / distribution.sum()
        
        distributions[region.region_id] = distribution
    
    return distributions, all_categories

def check_convenience(region_pois):
    """
    判断街区功能便利性的严格标准：
    1. POI类别数量 ≥ 7种
    2. POI总数 ≥ 20个  
    3. 包含所有4种基本服务类别（餐饮、购物、生活服务、交通设施）
    4. 综合便利性得分 ≥ 12分
    
    便利性得分计算：
    - 每种POI类别 +1分
    - 基本服务类别每种 +2分  
    - POI总数超过30个 +2分
    - POI总数超过50个 +3分
    - 覆盖所有基本服务类别 +3分
    """
    if region_pois.empty:
        return False, 0
    
    # 基本统计
    total_pois = len(region_pois)
    categories = set(region_pois['main_cat'].dropna())
    category_count = len(categories)
    
    # 检查基本条件
    if (category_count < PARAMS["convenience_min_categories"] or 
        total_pois < PARAMS["convenience_min_pois"]):
        return False, 0
    
    # 检查基本服务类别覆盖 - 必须全覆盖
    essential_cats = set(PARAMS["convenience_essential_categories"])
    covered_essential = essential_cats.intersection(categories)
    if len(covered_essential) < 4:  # 必须覆盖所有4种基本服务
        return False, 0
    
    # 计算便利性得分
    convenience_score = 0
    
    # 每种类别+1分
    convenience_score += category_count
    
    # 基本服务类别每种+2分
    convenience_score += len(covered_essential) * 2
    
    # POI数量奖励分（门槛提高）
    if total_pois > 50:
        convenience_score += 3
    elif total_pois > 30:
        convenience_score += 2
    
    # 全覆盖基本服务奖励分
    if len(covered_essential) == 4:
        convenience_score += 3
    
    # 综合判断（门槛提高到12分）
    is_convenient = convenience_score >= PARAMS["convenience_score_threshold"]
    
    return is_convenient, convenience_score

def generate_similarity_relations(l4_gdf, poi_gdf):
    """
    生成合并后的功能相似性关系 (2种)
    
    关系说明：
    - similarFunction: 功能分布相似 + POI数量相似 + 类别多样性相似 + 主导功能相同 + 功能互补
    - highConvenience: 街区功能便利性强 (种类齐全)
    """
    print_section("生成功能相似性关系")
    triples = []
    
    # 预计算数据
    distributions, _ = calculate_poi_distribution(l4_gdf, poi_gdf)
    region_ids = list(distributions.keys())
    
    # 预计算各区域的POI统计信息
    region_stats = {}
    convenience_regions = []
    
    print("预计算区域统计信息...")
    for _, region in show_progress(l4_gdf.iterrows(), "区域统计"):
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        
        stats = {
            'poi_count': len(region_pois),
            'categories': set(region_pois['main_cat'].dropna()),
            'category_count': region_pois['main_cat'].dropna().nunique(),
            'dominant_category': region_pois['main_cat'].mode().iloc[0] if len(region_pois) > 0 and not region_pois['main_cat'].mode().empty else None
        }
        
        region_stats[region.region_id] = stats
        
        # 检查便利性
        is_convenient, score = check_convenience(region_pois)
        if is_convenient:
            convenience_regions.append(region.region_id)
    
    print("1. similarFunction - 综合功能相似...")
    count = 0
    vectors = np.array([distributions[rid] for rid in region_ids])
    
    if len(vectors) > 0 and vectors.shape[1] > 0:
        region_pairs = [(i, j) for i in range(len(region_ids)) for j in range(i+1, len(region_ids))]
        
        for i, j in show_progress(region_pairs, "综合相似度计算"):
            region1_id = region_ids[i]
            region2_id = region_ids[j]
            stats1 = region_stats[region1_id]
            stats2 = region_stats[region2_id]
            
            similar = False
            
            # 原similarFunction: 功能分布相似
            if np.sum(vectors[i]) > 0 and np.sum(vectors[j]) > 0:
                similarity = 1 - cosine(vectors[i], vectors[j])
                if similarity >= PARAMS["function_similarity_threshold"]:
                    similar = True
            
            # 原poiCountSimilar: POI数量相似
            if not similar and stats1['poi_count'] > 0 and stats2['poi_count'] > 0:
                ratio = min(stats1['poi_count'], stats2['poi_count']) / max(stats1['poi_count'], stats2['poi_count'])
                if ratio >= PARAMS["poi_count_similarity_threshold"]:
                    similar = True
            
            # 原categoryDiversitySimilar: 类别多样性相似
            if not similar and stats1['category_count'] > 0 and stats2['category_count'] > 0:
                ratio = min(stats1['category_count'], stats2['category_count']) / max(stats1['category_count'], stats2['category_count'])
                if ratio >= PARAMS["category_diversity_threshold"]:
                    similar = True
            
            # 原dominantFunctionSimilar: 主导功能相同
            if not similar and stats1['dominant_category'] and stats2['dominant_category']:
                if stats1['dominant_category'] == stats2['dominant_category']:
                    similar = True
            
            # 原functionalComplement: 功能互补
            if not similar:
                for source_cat, target_cat in FUNCTIONAL_COMPLEMENTS:
                    if (source_cat in stats1['categories'] and target_cat in stats2['categories']) or \
                       (source_cat in stats2['categories'] and target_cat in stats1['categories']):
                        similar = True
                        break
            
            if similar:
                triples.append((region1_id, "similarFunction", region2_id))
                count += 1
    
    print(f"   生成 {count:,} 个 similarFunction 三元组")
    
    print("2. highConvenience - 功能便利性...")
    count = 0
    
    # 在便利性街区之间建立关系
    if len(convenience_regions) > 1:
        # 计算便利性街区之间的距离，建立相互关系
        l4_centroids = l4_gdf.copy()
        l4_centroids.geometry = l4_centroids.geometry.centroid
        
        convenience_pairs = [(r1, r2) for i, r1 in enumerate(convenience_regions) 
                           for j, r2 in enumerate(convenience_regions) if i < j]
        
        for region1_id, region2_id in show_progress(convenience_pairs, "便利性区域关系"):
            # 获取区域中心点
            region1_geom = l4_gdf[l4_gdf.region_id == region1_id].geometry.centroid.iloc[0]
            region2_geom = l4_gdf[l4_gdf.region_id == region2_id].geometry.centroid.iloc[0]
            distance = region1_geom.distance(region2_geom)
            
            # 如果距离在合理范围内，建立便利性关系
            if distance < PARAMS["convenience_distance_threshold"]:
                triples.append((region1_id, "highConvenience", region2_id))
                count += 1
    
    print(f"   生成 {count:,} 个 highConvenience 三元组")
    print(f"   便利性区域数量: {len(convenience_regions)}/{len(l4_gdf)}")
    print(f"   便利性区域列表: {', '.join(convenience_regions[:10])}" + 
          (f"..." if len(convenience_regions) > 10 else ""))
    
    print(f"✅ 相似性关系总计: {len(triples):,} 个三元组")
    return triples

def generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf):
    """
    生成合并后的移动性关系 (1种)
    
    关系说明：
    - flowTransition: 实际人员流动 + 模拟流动 + 吸引流动
    """
    print_section("生成移动性关系")
    triples = []
    
    print("flowTransition - 综合流动关系...")
    count = 0
    
    # 1. 实际流动 (基于签到数据)
    if not checkin_gdf.empty and "发布时间" in checkin_gdf.columns:
        try:
            checkin_gdf["timestamp"] = pd.to_datetime(checkin_gdf["发布时间"])
            
            if pd.notna(checkin_gdf["timestamp"]).any():
                user_groups = list(checkin_gdf.groupby("账号"))
                
                for _, group in show_progress(user_groups, "实际流动分析", show_details=False):
                    group = group.sort_values("timestamp")
                    
                    for i in range(len(group) - 1):
                        current = group.iloc[i]
                        next_checkin = group.iloc[i+1]
                        
                        if pd.notna(current.region_id) and pd.notna(next_checkin.region_id):
                            time_diff = (next_checkin.timestamp - current.timestamp).total_seconds()
                            
                            if (time_diff < PARAMS["flow_time_threshold"] and 
                                current.region_id != next_checkin.region_id):
                                triples.append((current.region_id, "flowTransition", next_checkin.region_id))
                                count += 1
        except Exception as e:
            print(f"   ⚠️ 签到数据处理失败: {e}")
    
    # 2. 模拟流动 (基于POI类型)
    l4_centroids = l4_gdf.copy()
    l4_centroids.geometry = l4_centroids.geometry.centroid
    
    region_categories = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        region_categories[region.region_id] = set(region_pois['main_cat'].dropna())
    
    for source_cat, target_cat in show_progress(FLOW_PATTERNS, "模拟流动分析"):
        source_regions = [rid for rid, cats in region_categories.items() if source_cat in cats]
        target_regions = [rid for rid, cats in region_categories.items() if target_cat in cats]
        
        for source_id in source_regions:
            for target_id in target_regions:
                if source_id != target_id:
                    source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
                    target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
                    distance = source_geom.distance(target_geom)
                    
                    if distance < PARAMS["flow_distance_threshold"]:
                        triples.append((source_id, "flowTransition", target_id))
                        count += 1
    
    # 3. 吸引流动 (向高吸引力区域)
    attraction_scores = {}
    for _, region in l4_gdf.iterrows():
        region_pois = poi_gdf[poi_gdf.region_id == region.region_id]
        score = 0
        
        for cat in HIGH_ATTRACTION_CATEGORIES:
            score += len(region_pois[region_pois['main_cat'] == cat])
        
        if score > 0:
            attraction_scores[region.region_id] = score
    
    attraction_pairs = [(target_id, source_region.region_id) 
                       for target_id in attraction_scores.keys() 
                       for _, source_region in l4_gdf.iterrows() 
                       if source_region.region_id != target_id]
    
    for target_id, source_id in show_progress(attraction_pairs, "吸引流动分析", show_details=False):
        target_geom = l4_gdf[l4_gdf.region_id == target_id].geometry.centroid.iloc[0]
        source_geom = l4_gdf[l4_gdf.region_id == source_id].geometry.centroid.iloc[0]
        distance = source_geom.distance(target_geom)
        
        if distance < PARAMS["attraction_distance_threshold"]:
            attraction_score = attraction_scores[target_id]
            attraction_prob = attraction_score / (1 + distance / 500)
            if attraction_prob > 2:
                triples.append((source_id, "flowTransition", target_id))
                count += 1
    
    print(f"   生成 {count:,} 个 flowTransition 三元组")
    
    print(f"✅ 移动性关系总计: {len(triples):,} 个三元组")
    return triples

def generate_categorical_relations(poi_gdf, bc_gdf):
    """生成分类关系 (2种)"""
    print_section("生成分类关系")
    triples = []
    
    print("1. cateOf - POI类别归属...")
    count = 0
    for _, row in show_progress(poi_gdf.iterrows(), "POI类别"):
        if pd.notna(row.category_id) and pd.notna(row.poi_id):
            triples.append((row.poi_id, "cateOf", row.category_id))
            count += 1
    
    print(f"   生成 {count:,} 个 cateOf 三元组")
    
    # print("2. belongTo - POI商圈归属...")
    # count = 0
    # for _, row in show_progress(poi_gdf.iterrows(), "POI商圈"):
    #     if pd.notna(row.bc_id):
    #         triples.append((row.poi_id, "belongTo", row.bc_id))
    #         count += 1
    #
    # print(f"   生成 {count:,} 个 belongTo 三元组")
    print("2. belongTo - POI商圈归属... [已停用]")
    print(f"   生成 0 个 belongTo 三元组")
    
    print(f"✅ 分类关系总计: {len(triples):,} 个三元组")
    return triples

def generate_service_relations(l4_gdf, bc_gdf):
    """生成服务关系 (1种)"""
    print_section("生成服务关系")
    triples = []
    
    print("1. provideService - 商圈服务...")
    count = 0
    
    service_pairs = [(bc.Index, region.Index, bc, region) 
                    for bc in bc_gdf.itertuples() 
                    for region in l4_gdf.itertuples()]
    
    for bc_idx, region_idx, bc, region in show_progress(service_pairs, "商圈服务检测"):
        if (bc.geometry.contains(region.geometry.centroid) or 
            bc.geometry.intersects(region.geometry)):
            triples.append((bc.bc_id, "provideService", region.region_id))
            count += 1
    
    print(f"   生成 {count:,} 个 provideService 三元组")
    
    print(f"✅ 服务关系总计: {len(triples):,} 个三元组")
    return triples

# ==================== 主函数 ====================

def save_triples_with_stats(triples, output_path):
    """保存三元组并生成统计信息"""
    print_section("保存三元组和统计")
    
    print("去重三元组...")
    unique_triples = list(set(triples))
    print(f"去重前: {len(triples):,} 个三元组")
    print(f"去重后: {len(unique_triples):,} 个三元组")
    
    # 保存三元组
    print("保存三元组到文件...")
    ensure_dir(output_path)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for head, relation, tail in show_progress(unique_triples, "写入三元组"):
            f.write(f"{head}\t{relation}\t{tail}\n")
    
    # 统计关系类型
    print("统计关系类型...")
    relation_counts = {}
    for _, relation, _ in show_progress(unique_triples, "统计关系"):
        relation_counts[relation] = relation_counts.get(relation, 0) + 1
    
    # 保存统计信息
    print("生成统计报告...")
    stats_df = pd.DataFrame([
        {'relation_type': rel, 'count': count, 'percentage': count/len(unique_triples)*100}
        for rel, count in sorted(relation_counts.items(), key=lambda x: x[1], reverse=True)
    ])
    
    ensure_dir(OUTPUT_PATHS["relation_stats"])
    stats_df.to_csv(OUTPUT_PATHS["relation_stats"], index=False)
    
    # 打印统计信息
    print_section("知识图谱统计")
    print(f"三元组总数: {len(unique_triples):,}")
    print(f"关系类型数: {len(relation_counts)}")
    print("\n各关系类型分布:")
    for _, row in stats_df.iterrows():
        print(f"  {row['relation_type']:<20}: {row['count']:>7,} ({row['percentage']:>5.1f}%)")
    
    return unique_triples, relation_counts

def estimate_connectivity(triples, region_count):
    """估算图连通性"""
    print_section("连通性分析")
    
    # 区域间连接关系
    spatial_relations = ['borderBy', 'nearBy', 'locateAt']
    similarity_relations = ['similarFunction', 'highConvenience']
    mobility_relations = ['flowTransition']
    
    spatial_count = len([t for t in triples if t[1] in spatial_relations])
    similarity_count = len([t for t in triples if t[1] in similarity_relations])
    mobility_count = len([t for t in triples if t[1] in mobility_relations])
    
    # 计算区域间连接数（排除POI相关的locateAt关系）
    region_connections = len([t for t in triples if t[1] in ['borderBy', 'nearBy', 'similarFunction', 'flowTransition']])
    avg_connections = (region_connections * 2) / region_count if region_count > 0 else 0
    
    print(f"区域总数: {region_count:,}")
    print(f"空间连接: {spatial_count:,}")
    print(f"相似性连接: {similarity_count:,}")
    print(f"移动性连接: {mobility_count:,}")
    print(f"区域间连接总数: {region_connections:,}")
    print(f"估算平均每区域连接数: {avg_connections:.2f}")
    
    if avg_connections > 8:
        print("✅ 连通性优秀！图结构非常适合GNN模型")
    elif avg_connections > 5:
        print("✅ 连通性良好！预期解决连通性问题")
    elif avg_connections > 3:
        print("⚠️ 连通性一般，可能需要进一步优化")
    else:
        print("❌ 连通性仍然较差，建议检查参数设置")

def main():
    """主函数"""
    try:
        print_section("🚀 优化版无建筑物知识图谱生成器启动")
        print(f"📂 输出目录: {OUTPUT_BASE_PATH}")
        start_time = time.time()
        
        # 1. 数据加载
        l4_gdf, poi_gdf, bc_gdf, checkin_df = load_data()
        
        # 2. 数据预处理
        l4_gdf, poi_gdf, bc_gdf, checkin_gdf = preprocess_data(l4_gdf, poi_gdf, bc_gdf, checkin_df)
        
        # 3. 生成各类关系
        all_triples = []
        
        print_section("🔗 开始生成知识图谱关系")
        
        relation_tasks = [
            ("空间关系", lambda: generate_spatial_relations(l4_gdf, poi_gdf)),
            ("相似性关系", lambda: generate_similarity_relations(l4_gdf, poi_gdf)),
            ("移动性关系", lambda: generate_mobility_relations(l4_gdf, poi_gdf, checkin_gdf)),
            ("分类关系", lambda: generate_categorical_relations(poi_gdf, bc_gdf)),
            ("服务关系", lambda: generate_service_relations(l4_gdf, bc_gdf))
        ]
        
        for task_name, task_func in show_detailed_progress(relation_tasks, "生成关系类型"):
            task_start = time.time()
            triples = task_func()
            all_triples.extend(triples)
            task_time = time.time() - task_start
            
            tqdm.write(f"✅ {task_name}: {len(triples):,} 个三元组 (耗时: {task_time:.1f}s)")
        
        # 4. 保存结果并统计
        unique_triples, final_relation_counts = save_triples_with_stats(all_triples, OUTPUT_PATHS["kg_without_building"])
        
        # 5. 连通性分析
        estimate_connectivity(unique_triples, len(l4_gdf))
        
        # 6. 总结
        total_time = time.time() - start_time
        print_section("🎉 生成完成")
        print(f"✅ 总耗时: {total_time:.1f} 秒")
        print(f"✅ 处理速度: {len(unique_triples)/total_time:.0f} 三元组/秒")
        print(f"✅ 知识图谱已保存: {OUTPUT_PATHS['kg_without_building']}")
        print(f"✅ 统计信息已保存: {OUTPUT_PATHS['relation_stats']}")
        print(f"✅ 类别映射已保存: {OUTPUT_PATHS['category_mapping']}")
        
        # 便利性判断标准说明
        print_section("📋 便利性判断标准（更严格）")
        print("街区功能便利性(highConvenience)判断标准：")
        print(f"1. POI类别数量 ≥ {PARAMS['convenience_min_categories']}种")
        print(f"2. POI总数 ≥ {PARAMS['convenience_min_pois']}个")
        print("3. 必须包含所有4种基本服务类别：")
        for cat in PARAMS['convenience_essential_categories']:
            print(f"   - {cat}")
        print(f"4. 综合便利性得分 ≥ {PARAMS['convenience_score_threshold']}分")
        print(f"5. 便利性街区间距离 < {PARAMS['convenience_distance_threshold']}米时建立关系")
        print("\n便利性得分计算方法（更严格）：")
        print("- 每种POI类别 +1分")
        print("- 基本服务类别每种 +2分")  
        print("- POI总数超过30个 +2分")
        print("- POI总数超过50个 +3分")
        print("- 覆盖所有基本服务类别 +3分")
        print("\n关系形式: (Region_A highConvenience Region_B)")
        print("表示两个便利性街区之间的功能便利性关联")
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成知识图谱时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())