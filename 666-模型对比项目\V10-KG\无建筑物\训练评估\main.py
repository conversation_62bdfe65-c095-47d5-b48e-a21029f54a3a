import os
import torch
import torch.nn as nn
import numpy as np
import argparse
import json
from torch.utils.data import DataLoader, TensorDataset
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置matplotlib字体以支持中文
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']  # 使用英文字体
plt.rcParams['axes.unicode_minus'] = False
# 如果需要中文，可以尝试以下设置（需要系统有对应字体）
# plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']

from load_data import Data_CompGCN
from cal import in_out_norm
from config import get_config

# 导入所有模型
model_imports = {}

# RGCN
try:
    from models.rgcn import RGCN
    model_imports['RGCN'] = RGCN
    print("Success: RGCN model imported")
except ImportError as e:
    print(f"Error: RGCN model import failed: {e}")

# GraphSAGE
try:
    from models.graphsage import GraphSAGE
    model_imports['GraphSAGE'] = GraphSAGE
    print("Success: GraphSAGE model imported")
except ImportError as e:
    print(f"Error: GraphSAGE model import failed: {e}")

# GAT
try:
    from models.gat import GAT
    model_imports['GAT'] = GAT
    print("Success: GAT model imported")
except ImportError as e:
    print(f"Error: GAT model import failed: {e}")

# HAN
try:
    from models.han import HAN
    model_imports['HAN'] = HAN
    print("Success: HAN model imported")
except ImportError as e:
    print(f"Error: HAN model import failed: {e}")

# KGAT
try:
    from models.kgat import KGAT
    model_imports['KGAT'] = KGAT
    print("Success: KGAT model imported")
except ImportError as e:
    print(f"Error: KGAT model import failed: {e}")

# CompGCN
try:
    from models.compGCN import CompGraphConv
    import torch.nn.functional as F  # 添加F的导入
    # 创建CompGCN包装类
    class CompGCN(nn.Module):
        def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
            super(CompGCN, self).__init__()
            self.g = kwargs['g']
            self.d = kwargs['d']
            
            # 节点和关系嵌入
            self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
            self.rel_embeds = nn.Embedding.from_pretrained(rel_emb, freeze=True)
            
            # CompGCN层
            self.layers = nn.ModuleList()
            layer_dims = [node_emb.shape[1]] + layer_sizes
            
            for i in range(len(layer_sizes)):
                self.layers.append(CompGraphConv(
                    layer_dims[i],
                    layer_dims[i+1],
                    comp_fn='rotate',
                    dropout=dropout_list[i]
                ))
            
            # 预测器
            self.projector = nn.Sequential(
                nn.Linear(layer_sizes[-1], layer_sizes[-1]),
                nn.ReLU(),
                nn.Linear(layer_sizes[-1], layer_sizes[-1])
            )
            
            # 准备边的方向标记
            self._prepare_edge_directions()
        
        def _prepare_edge_directions(self):
            """准备边的方向信息"""
            # 标记出边和入边 - 确保在正确的设备上
            num_edges = self.g.num_edges()
            device = self.g.device
            self.g.edata['out_edges_mask'] = torch.ones(num_edges, dtype=torch.bool, device=device)
            self.g.edata['in_edges_mask'] = torch.zeros(num_edges, dtype=torch.bool, device=device)
            
            # 如果有反向边，标记它们
            if hasattr(self.d, '_has_reverse_edges') and self.d._has_reverse_edges:
                half_edges = num_edges // 2
                self.g.edata['in_edges_mask'][half_edges:] = True
                self.g.edata['out_edges_mask'][half_edges:] = False
        
        def forward(self, node_idx):
            """前向传播"""
            h = self.node_embeds.weight
            r = self.rel_embeds.weight
            
            # 通过CompGCN层
            for layer in self.layers:
                h, r = layer(self.g, h, r)
            
            # 获取特定节点的嵌入
            h_node = h[node_idx]
            
            # 特征投影
            h_proj = self.projector(h_node)
            
            # 自监督损失
            return F.mse_loss(h_proj, h_node.detach())
        
        def get_feature(self, node_idx):
            """提取特征"""
            with torch.no_grad():
                h = self.node_embeds.weight
                r = self.rel_embeds.weight
                
                # 通过CompGCN层
                for layer in self.layers:
                    h, r = layer(self.g, h, r)
                
                if node_idx is not None:
                    return h[node_idx]
                else:
                    return h
    
    model_imports['CompGCN'] = CompGCN
    print("Success: CompGCN model imported")
except ImportError as e:
    print(f"Error: CompGCN model import failed: {e}")

# 定义可用模型列表
available_models = list(model_imports.keys())

def set_seed(seed):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True


def get_model_graph_config(model_name):
    """获取不同模型的图配置需求"""
    model_configs = {
        'RGCN': {
            'add_reverse': True,
            'add_self_loop': True,
            'description': 'RGCN需要反向边和自环来处理多关系图'
        },
        'GraphSAGE': {
            'add_reverse': False,
            'add_self_loop': True,
            'description': 'GraphSAGE通过采样邻居，通常不需要反向边'
        },
        'GAT': {
            'add_reverse': False,
            'add_self_loop': True,
            'description': 'GAT通过注意力处理边的重要性'
        },
        'HAN': {
            'add_reverse': True,
            'add_self_loop': True,
            'description': 'HAN处理异构图，需要完整的边信息'
        },
        'KGAT': {
            'add_reverse': True,
            'add_self_loop': True,
            'description': 'KGAT使用知识图谱注意力机制'
        },
        'CompGCN': {
            'add_reverse': True,
            'add_self_loop': True,
            'description': 'CompGCN通过组合函数处理关系'
        }
    }
    
    return model_configs.get(model_name, {
        'add_reverse': False,
        'add_self_loop': False,
        'description': '未知模型，使用默认配置'
    })


def parse_args():
    parser = argparse.ArgumentParser(description="增强版知识图谱模型训练")
    parser.add_argument("--dataset", type=str, default="shenyang")
    parser.add_argument("--model", type=str, 
                       choices=list(model_imports.keys()), 
                       default="RGCN")
    parser.add_argument("--batch_size", type=int, default=128)
    parser.add_argument("--epochs", type=int, default=100)
    parser.add_argument("--lr", type=float, default=0.001)
    parser.add_argument("--hidden_dim", type=int, default=128)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--dropout", type=float, default=0.3)
    parser.add_argument("--extract_only", action="store_true")
    parser.add_argument("--model_path", type=str, default="./saved_models/")
    parser.add_argument("--validate", action="store_true", help="运行验证和可视化")
    parser.add_argument("--compare_all", action="store_true", help="比较所有模型")
    
    # 图结构配置参数
    parser.add_argument("--force_reverse", action="store_true")
    parser.add_argument("--no_reverse", action="store_true")
    parser.add_argument("--force_self_loop", action="store_true")
    parser.add_argument("--no_self_loop", action="store_true")
    
    return parser.parse_args()


def prepare_graph_for_model(data_loader, model_name, args):
    """为特定模型准备图结构"""
    # 获取模型默认配置
    model_config = get_model_graph_config(model_name)
    
    # 用户命令行参数可以覆盖默认配置
    add_reverse = model_config['add_reverse']
    add_self_loop = model_config['add_self_loop']
    
    # 处理命令行覆盖
    if args.force_reverse:
        add_reverse = True
    elif args.no_reverse:
        add_reverse = False
        
    if args.force_self_loop:
        add_self_loop = True
    elif args.no_self_loop:
        add_self_loop = False
    
    print(f"\n{model_name} graph structure configuration:")
    print(f"   - Model description: {model_config['description']}")
    print(f"   - Add reverse edges: {add_reverse}")
    print(f"   - Add self loops: {add_self_loop}")
    
    # 获取配置好的图
    graph = data_loader.get_graph_for_model(
        model_name=model_name,
        add_reverse=add_reverse,
        add_self_loop=add_self_loop
    )
    
    # 计算关系数量
    num_relations = data_loader.get_num_relations(
        include_reverse=add_reverse,
        include_self_loop=add_self_loop
    )
    
    return graph, num_relations


def train_epoch(model, dataloader, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    
    for batch_idx, (kg_idx,) in enumerate(dataloader):
        kg_idx = kg_idx.to(device)
        
        optimizer.zero_grad()
        loss = model(kg_idx)
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
    
    return total_loss / len(dataloader)


def extract_features(model, region_ids, device):
    """提取特征"""
    model.eval()
    with torch.no_grad():
        kg_indices = torch.tensor(region_ids, dtype=torch.long).to(device)
        features = model.get_feature(kg_indices)
        return features.cpu().numpy()


def validate_model(args, model, data_loader, region_ids, device):
    """验证模型性能并生成可视化"""
    print("\nStarting model validation...")
    
    # 1. 提取特征
    features = extract_features(model, region_ids, device)
    
    # 2. 创建验证目录
    val_dir = os.path.join(args.model_path, 'validation', args.model)
    os.makedirs(val_dir, exist_ok=True)
    
    # 3. 特征分析
    print("Feature statistics:")
    print(f"   - Feature dimension: {features.shape}")
    print(f"   - Feature mean: {np.mean(features):.4f}")
    print(f"   - Feature std: {np.std(features):.4f}")
    print(f"   - Feature range: [{np.min(features):.4f}, {np.max(features):.4f}]")
    
    # 4. 特征可视化
    plt.figure(figsize=(15, 10))
    
    # 4.1 特征分布直方图
    plt.subplot(2, 3, 1)
    plt.hist(features.flatten(), bins=50, alpha=0.7, color='blue')
    plt.title(f'{args.model} Feature Distribution')
    plt.xlabel('Feature Value')
    plt.ylabel('Frequency')
    
    # 4.2 特征热力图（前100个样本，前50个特征）
    plt.subplot(2, 3, 2)
    sns.heatmap(features[:100, :50], cmap='coolwarm', cbar=True)
    plt.title(f'{args.model} Feature Heatmap')
    plt.xlabel('Feature Dimension')
    plt.ylabel('Sample')
    
    # 4.3 特征相关性
    plt.subplot(2, 3, 3)
    feature_corr = np.corrcoef(features.T)
    sns.heatmap(feature_corr[:20, :20], cmap='coolwarm', cbar=True)
    plt.title('Feature Correlation Matrix')
    
    # 4.4 特征方差
    plt.subplot(2, 3, 4)
    feature_var = np.var(features, axis=0)
    plt.plot(sorted(feature_var, reverse=True))
    plt.title('Feature Variance Distribution')
    plt.xlabel('Feature Index (sorted by variance)')
    plt.ylabel('Variance')
    plt.yscale('log')
    
    # 4.5 PCA分析
    from sklearn.decomposition import PCA
    pca = PCA(n_components=2)
    features_pca = pca.fit_transform(features)
    
    plt.subplot(2, 3, 5)
    plt.scatter(features_pca[:, 0], features_pca[:, 1], alpha=0.6)
    plt.title('Feature PCA Projection')
    plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
    plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
    
    # 4.6 特征稀疏性
    plt.subplot(2, 3, 6)
    sparsity = np.sum(np.abs(features) < 0.01, axis=1) / features.shape[1]
    plt.hist(sparsity, bins=30, alpha=0.7, color='green')
    plt.title('Feature Sparsity Distribution')
    plt.xlabel('Sparsity (proportion close to 0)')
    plt.ylabel('Sample Count')
    
    plt.tight_layout()
    plt.savefig(os.path.join(val_dir, f'{args.model}_feature_analysis.png'), dpi=300)
    plt.close()
    
    # 5. 保存验证结果
    validation_results = {
        'model': args.model,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'feature_stats': {
            'shape': features.shape,
            'mean': float(np.mean(features)),
            'std': float(np.std(features)),
            'min': float(np.min(features)),
            'max': float(np.max(features)),
            'sparsity': float(np.mean(np.abs(features) < 0.01))
        },
        'pca_variance_ratio': pca.explained_variance_ratio_.tolist(),
        'graph_config': get_model_graph_config(args.model)
    }
    
    with open(os.path.join(val_dir, 'validation_results.json'), 'w') as f:
        json.dump(validation_results, f, indent=2)
    
    print(f"Validation completed, results saved in: {val_dir}")
    
    return features


def compare_all_models(args):
    """比较所有可用模型"""
    print("\nStarting multi-model comparison...")
    
    comparison_results = {}
    
    # 对每个可用模型进行训练和评估
    for model_name, model_class in model_imports.items():
        if model_class is None:
            continue
            
        print(f"\n{'='*60}")
        print(f"Training and evaluating {model_name}")
        print(f"{'='*60}")
        
        # 设置模型参数
        args.model = model_name
        
        try:
            # 训练模型
            results = train_and_evaluate_model(args)
            comparison_results[model_name] = results
            
        except Exception as e:
            print(f"Error: {model_name} training failed: {e}")
            comparison_results[model_name] = {'error': str(e)}
    
    # 生成比较报告
    generate_comparison_report(comparison_results, args)


def train_and_evaluate_model(args):
    """训练和评估单个模型"""
    # 基本设置
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    set_seed(args.seed)
    
    # 加载数据
    d = Data_CompGCN(args.kg_path, args.dataset)
    
    # 为模型准备图结构
    g, num_relations = prepare_graph_for_model(d, args.model, args)
    
    # 获取区域实体
    region_ids = [ent_id for ent_name, ent_id in d.ent2id.items() 
                  if "Region" in ent_name or "region" in ent_name.lower()]
    
    if not region_ids:
        region_ids = list(range(min(100, g.num_nodes())))
    
    # 准备数据
    dataset = TensorDataset(torch.tensor(region_ids, dtype=torch.long))
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True)
    
    # 图预处理
    g = in_out_norm(g.to(device))
    
    # 初始化嵌入
    num_nodes = g.num_nodes()
    node_emb = torch.randn(num_nodes, args.hidden_dim) * 0.1
    rel_emb = torch.randn(num_relations, args.hidden_dim) * 0.1
    
    # 初始化模型
    model_class = model_imports[args.model]
    model_kwargs = {
        'd': d, 'g': g,
        'layer_sizes': [args.hidden_dim, args.hidden_dim],
        'dropout_list': [args.dropout, args.dropout]
    }
    
    model = model_class(node_emb, rel_emb, **model_kwargs).to(device)
    
    # 训练
    print(f"\nStarting {args.model} training...")
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    train_losses = []
    best_loss = float('inf')
    
    for epoch in range(args.epochs):
        avg_loss = train_epoch(model, dataloader, optimizer, device)
        train_losses.append(avg_loss)
        scheduler.step(avg_loss)
        
        if (epoch + 1) % 10 == 0 or epoch == 0:
            print(f"Epoch {epoch+1:3d}/{args.epochs}, Loss: {avg_loss:.4f}")
        
        if avg_loss < best_loss:
            best_loss = avg_loss
            model_save_path = os.path.join(args.model_path, f"{args.dataset}_{args.model}.pt")
            os.makedirs(args.model_path, exist_ok=True)
            torch.save(model.state_dict(), model_save_path)
    
    # 提取特征并预测
    features = extract_features(model, region_ids, device)
    
    # 能耗预测
    try:
        from utils.mlp import train_energy_predictor
        results = train_energy_predictor(args, features, args.region_info_path)
        
        return {
            'train_losses': train_losses,
            'best_loss': float(best_loss),
            'r2': float(results[1]),
            'rmse': float(results[2]),
            'mae': float(results[3]),
            'mape': float(results[4]),
            'feature_shape': features.shape
        }
        
    except Exception as e:
        print(f"❌ 能耗预测失败: {e}")
        return {
            'train_losses': train_losses,
            'best_loss': float(best_loss),
            'error': str(e)
        }


def generate_comparison_report(results, args):
    """生成多模型比较报告"""
    report_dir = os.path.join(args.model_path, 'model_comparison')
    os.makedirs(report_dir, exist_ok=True)
    
    # 1. 创建比较图表
    plt.figure(figsize=(20, 15))
    
    # 1.1 训练损失对比
    plt.subplot(2, 3, 1)
    for model_name, result in results.items():
        if 'train_losses' in result:
            plt.plot(result['train_losses'], label=model_name)
    plt.title('Training Loss Comparison')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.yscale('log')
    
    # 1.2 R²得分对比
    plt.subplot(2, 3, 2)
    model_names = []
    r2_scores = []
    for model_name, result in results.items():
        if 'r2' in result:
            model_names.append(model_name)
            r2_scores.append(result['r2'])
    
    bars = plt.bar(model_names, r2_scores, color=['blue', 'green', 'red', 'orange', 'purple', 'brown'])
    plt.title('R² Score Comparison')
    plt.ylabel('R² Score')
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.3)
    
    # 添加数值标签
    for bar, score in zip(bars, r2_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    # 1.3 RMSE对比
    plt.subplot(2, 3, 3)
    rmse_scores = []
    for model_name in model_names:
        if 'rmse' in results[model_name]:
            rmse_scores.append(results[model_name]['rmse'])
    
    bars = plt.bar(model_names, rmse_scores, color=['blue', 'green', 'red', 'orange', 'purple', 'brown'])
    plt.title('RMSE Comparison')
    plt.ylabel('RMSE')
    
    for bar, score in zip(bars, rmse_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    # 1.4 MAE对比
    plt.subplot(2, 3, 4)
    mae_scores = []
    for model_name in model_names:
        if 'mae' in results[model_name]:
            mae_scores.append(results[model_name]['mae'])
    
    bars = plt.bar(model_names, mae_scores, color=['blue', 'green', 'red', 'orange', 'purple', 'brown'])
    plt.title('MAE Comparison')
    plt.ylabel('MAE')
    
    for bar, score in zip(bars, mae_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    # 1.5 综合性能雷达图
    ax5 = plt.subplot(2, 3, 5, projection='polar')  # 指定极坐标投影
    from math import pi
    
    categories = ['R²', 'RMSE⁻¹', 'MAE⁻¹', 'Training']
    
    for model_name in model_names[:3]:  # 只显示前3个模型
        if all(k in results[model_name] for k in ['r2', 'rmse', 'mae', 'best_loss']):
            values = [
                (results[model_name]['r2'] + 1) / 2,  # 归一化到[0,1]
                1 / (1 + results[model_name]['rmse']),  # 倒数归一化
                1 / (1 + results[model_name]['mae']),   # 倒数归一化
                1 / (1 + results[model_name]['best_loss'])  # 倒数归一化
            ]
            
            angles = [n / len(categories) * 2 * pi for n in range(len(categories))]
            values += values[:1]
            angles += angles[:1]
            
            ax5.plot(angles, values, 'o-', linewidth=2, label=model_name)
            ax5.fill(angles, values, alpha=0.25)
    
    ax5.set_xticks(angles[:-1])
    ax5.set_xticklabels(categories)
    ax5.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    ax5.set_title('Performance Comparison')
    
    # 1.6 性能排名
    plt.subplot(2, 3, 6)
    plt.text(0.1, 0.9, 'Model Performance Ranking', fontsize=16, weight='bold', transform=plt.gca().transAxes)
    
    # 按R²排序
    sorted_models = sorted([(name, res['r2']) for name, res in results.items() if 'r2' in res], 
                          key=lambda x: x[1], reverse=True)
    
    y_pos = 0.7
    for i, (model_name, r2) in enumerate(sorted_models):
        medal = ['1st', '2nd', '3rd'][i] if i < 3 else f'{i+1}.'
        text = f"{medal} {model_name}: R²={r2:.4f}"
        plt.text(0.1, y_pos, text, fontsize=12, transform=plt.gca().transAxes)
        y_pos -= 0.1
    
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(report_dir, 'model_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 保存详细报告
    with open(os.path.join(report_dir, 'comparison_results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # 3. 生成Markdown报告
    generate_markdown_report(results, report_dir)
    
    print("\n✅ Model comparison report generated: " + report_dir)


def generate_markdown_report(results, report_dir):
    """生成Markdown格式的比较报告"""
    report_content = f"""# Knowledge Graph Model Performance Comparison Report

Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Model Performance Overview

| Model | R² | RMSE | MAE | MAPE(%) | Best Loss |
|------|-----|------|-----|---------|----------|
"""
    
    for model_name, result in results.items():
        if 'error' not in result:
            r2 = result.get('r2', 'N/A')
            rmse = result.get('rmse', 'N/A')
            mae = result.get('mae', 'N/A')
            mape = result.get('mape', 'N/A')
            best_loss = result.get('best_loss', 'N/A')
            
            report_content += f"| {model_name} | {r2:.4f} | {rmse:.4f} | {mae:.4f} | {mape:.2f} | {best_loss:.4f} |\n"
    
    report_content += """
## Performance Ranking

### By R² Score (Higher is Better)
"""
    
    sorted_by_r2 = sorted([(name, res.get('r2', -999)) for name, res in results.items() 
                          if 'error' not in res], key=lambda x: x[1], reverse=True)
    
    for i, (model_name, r2) in enumerate(sorted_by_r2):
        medal = ['1st', '2nd', '3rd'][i] if i < 3 else f'{i+1}.'
        report_content += f"{medal} **{model_name}**: R²={r2:.4f}\n"
    
    report_content += """
### By RMSE (Lower is Better)
"""
    
    sorted_by_rmse = sorted([(name, res.get('rmse', 999)) for name, res in results.items() 
                            if 'error' not in res], key=lambda x: x[1])
    
    for i, (model_name, rmse) in enumerate(sorted_by_rmse):
        medal = ['1st', '2nd', '3rd'][i] if i < 3 else f'{i+1}.'
        report_content += f"{medal} **{model_name}**: RMSE={rmse:.4f}\n"
    
    report_content += """
## Analysis and Recommendations

"""
    
    # 找出最佳模型
    if sorted_by_r2:
        best_model = sorted_by_r2[0][0]
        report_content += f"1. **Best Model**: {best_model} achieved the highest R² score\n"
    
    # 分析负R²问题
    negative_r2_models = [name for name, res in results.items() 
                         if 'r2' in res and res['r2'] < 0]
    
    if negative_r2_models:
        report_content += f"2. **Need Optimization**: {', '.join(negative_r2_models)} have negative R², suggestions:\n"
        report_content += "   - Increase regularization\n"
        report_content += "   - Reduce model complexity\n"
        report_content += "   - Check data quality\n"
    
    # 保存报告
    with open(os.path.join(report_dir, 'comparison_report.md'), 'w', encoding='utf-8') as f:
        f.write(report_content)


def main():
    args = parse_args()
    
    # 加载配置
    try:
        config = get_config(args.dataset)
        for key, value in config.items():
            setattr(args, key, value)
    except:
        print("Warning: 使用默认配置")
    
    # 检查模型是否可用
    if args.model not in model_imports or model_imports[args.model] is None:
        print(f"Error: Model {args.model} is not available")
        print(f"Available models: {[k for k, v in model_imports.items() if v is not None]}")
        return
    
    # 比较所有模型
    if args.compare_all:
        compare_all_models(args)
        return
    
    # 训练或评估单个模型
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    set_seed(args.seed)
    print(f"使用设备: {device}")
    
    # 加载数据
    print(f"Loading {args.dataset} data...")
    d = Data_CompGCN(args.kg_path, args.dataset)
    
    # 为指定模型准备图结构
    g, num_relations = prepare_graph_for_model(d, args.model, args)
    
    # 获取区域实体
    region_ids = [ent_id for ent_name, ent_id in d.ent2id.items() 
                  if "Region" in ent_name or "region" in ent_name.lower()]
    if not region_ids:
        region_ids = list(range(min(100, g.num_nodes())))
    print(f"Found {len(region_ids)} region entities")
    
    # 准备数据
    dataset = TensorDataset(torch.tensor(region_ids, dtype=torch.long))
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True)
    
    # 图预处理
    g = in_out_norm(g.to(device))
    
    # 初始化嵌入
    num_nodes = g.num_nodes()
    node_emb = torch.randn(num_nodes, args.hidden_dim) * 0.1
    rel_emb = torch.randn(num_relations, args.hidden_dim) * 0.1
    
    print(f"Model parameters:")
    print(f"   - Nodes: {num_nodes}")
    print(f"   - Relations: {num_relations}")
    print(f"   - Hidden dimension: {args.hidden_dim}")
    
    # 初始化模型
    model_class = model_imports[args.model]
    model_kwargs = {
        'd': d, 'g': g,
        'layer_sizes': [args.hidden_dim, args.hidden_dim],
        'dropout_list': [args.dropout, args.dropout]
    }
    
    model = model_class(node_emb, rel_emb, **model_kwargs).to(device)
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练或提取特征
    model_save_path = os.path.join(args.model_path, f"{args.dataset}_{args.model}.pt")
    
    if not args.extract_only:
        # 训练模式
        print(f"\n🚀 开始训练 {args.model} 模型...")
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr, weight_decay=1e-4)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        best_loss = float('inf')
        train_losses = []
        
        for epoch in range(args.epochs):
            avg_loss = train_epoch(model, dataloader, optimizer, device)
            train_losses.append(avg_loss)
            scheduler.step(avg_loss)
            
            if (epoch + 1) % 10 == 0 or epoch == 0:
                print(f"Epoch {epoch+1:3d}/{args.epochs}, Loss: {avg_loss:.4f}, LR: {optimizer.param_groups[0]['lr']:.6f}")
            
            if avg_loss < best_loss:
                best_loss = avg_loss
                os.makedirs(args.model_path, exist_ok=True)
                torch.save(model.state_dict(), model_save_path)
        
        print(f"Training completed! Model saved to: {model_save_path}")
        print(f"   Best loss: {best_loss:.4f}")
        
        # 绘制训练曲线
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses)
        plt.title(f'{args.model} Training Loss Curve')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.yscale('log')
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(args.model_path, f'{args.model}_training_curve.png'))
        plt.close()
        
    else:
        # 提取特征模式
        if os.path.exists(model_save_path):
            model.load_state_dict(torch.load(model_save_path, map_location=device))
            print(f"Loaded model: {model_save_path}")
        else:
            print(f"Warning: Model file not found, using randomly initialized model")
        
        # 验证模式
        if args.validate:
            features = validate_model(args, model, d, region_ids, device)
        else:
            print(f"\nExtracting {args.model} features...")
            features = extract_features(model, region_ids, device)
            print(f"Feature extraction completed: {features.shape}")
        
        # 调用MLP进行能耗预测
        try:
            from utils.mlp import train_energy_predictor
            
            # 检查区域信息文件是否存在
            region_info_path = getattr(args, 'region_info_path', None)
            if region_info_path and os.path.exists(region_info_path):
                print(f"\nStarting energy prediction based on {args.model} features...")
                results = train_energy_predictor(args, features, region_info_path)
                
                print(f"\n=== {args.model} Model Final Prediction Results ===")
                print(f"R² score: {results[1]:.4f}")
                print(f"RMSE: {results[2]:.4f}")
                print(f"MAE: {results[3]:.4f}")
                print(f"MAPE: {results[4]:.4f}%")
                
                # 创建预测可视化
                plt.figure(figsize=(12, 8))
                
                # 预测vs真实值散点图
                plt.subplot(2, 2, 1)
                plt.scatter(results[5], results[0], alpha=0.6)
                plt.plot([results[5].min(), results[5].max()], 
                        [results[5].min(), results[5].max()], 'r--', lw=2)
                plt.xlabel('True Values')
                plt.ylabel('Predicted Values')
                plt.title(f'{args.model} Predictions vs True Values')
                
                # 残差图
                plt.subplot(2, 2, 2)
                residuals = results[5] - results[0]
                plt.scatter(results[0], residuals, alpha=0.6)
                plt.axhline(y=0, color='r', linestyle='--')
                plt.xlabel('Predicted Values')
                plt.ylabel('Residuals')
                plt.title('Residual Distribution')
                
                # 残差直方图
                plt.subplot(2, 2, 3)
                plt.hist(residuals, bins=30, alpha=0.7, color='green')
                plt.xlabel('Residuals')
                plt.ylabel('Frequency')
                plt.title('Residual Histogram')
                
                # 性能指标
                plt.subplot(2, 2, 4)
                metrics = ['R²', 'RMSE', 'MAE', 'MAPE(%)']
                values = [results[1], results[2], results[3], results[4]]
                bars = plt.bar(metrics, values, color=['blue', 'green', 'orange', 'red'])
                plt.title('Performance Metrics')
                
                # 添加数值标签
                for bar, value in zip(bars, values):
                    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                            f'{value:.3f}', ha='center', va='bottom')
                
                plt.tight_layout()
                plt.savefig(os.path.join(args.model_path, f'{args.model}_prediction_analysis.png'), dpi=300)
                plt.close()
                
                print(f"Prediction analysis plot saved")
                
            else:
                print(f"Error: Region info file not found: {region_info_path}")
                print("Please check region_info_path in config.py")
                
        except ImportError as e:
            print(f"Error: Cannot import MLP module: {e}")
        except Exception as e:
            print(f"Error: Prediction failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    print("=" * 80)
    print("Enhanced Knowledge Graph-based Urban Energy Consumption Prediction System")
    print("=" * 80)
    print(f"Available models: {', '.join(available_models)}")
    
    main()
    
    print("\n" + "=" * 80)
    print("Program execution completed")
    print("=" * 80)