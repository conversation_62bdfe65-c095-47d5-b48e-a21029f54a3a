{"data_loading": {"region_info_count": 757, "kg_entities_count": 200558, "kg_relations_count": 19, "kg_triples_count": 727578, "train_count": 605, "val_count": 75, "test_count": 77, "total_unique_blocks": 757}, "alignment_check": {"kg_aligned": 757, "energy_aligned": 757, "fully_aligned": 757, "missing_details": {}}, "kg_analysis_enhanced": {"basic_stats": {"nodes": 200558, "edges": 719509, "avg_degree": 7.175071550374455}, "connectivity": {"is_strongly_connected": false, "num_scc": 199817}, "entity_types": {"Other": 694454, "POI": 221820, "Region": 537345, "Facility": 1537}, "relation_types": {"belongsToLand": 78283, "cateOf": 110910, "hasPhysicalAttribute": 86647, "hasFunction": 86647, "locateAt": 110910, "provideService": 594, "functionalSimilarity": 198441, "connectedTo": 37140, "nearBy": 6708, "densityInfluence": 3579, "belongsToRegion": 526, "flowTransition": 663, "highConvenience": 2737, "hasMorphology": 2187, "hasDominantFunction": 757, "similarFunction": 146, "hasLandUse": 428, "functionalComplementarity": 151, "similarMorphology": 124}, "region_connections": {"Region_640": 657, "Region_46": 1091, "Region_454": 648, "Region_95": 1247, "Region_661": 500, "Region_315": 585, "Region_583": 694, "Region_148": 1007, "Region_451": 726, "Region_221": 848, "Region_689": 1097, "Region_179": 1067, "Region_261": 560, "Region_353": 606, "Region_232": 689, "Region_397": 717, "Region_286": 2206, "Region_395": 728, "Region_634": 726, "Region_61": 812, "Region_596": 1005, "Region_242": 573, "Region_296": 747, "Region_299": 1553, "Region_749": 575, "Region_270": 915, "Region_474": 674, "Region_188": 795, "Region_381": 630, "Region_323": 650, "Region_391": 782, "Region_602": 967, "Region_694": 855, "Region_127": 856, "Region_268": 701, "Region_309": 646, "Region_480": 984, "Region_31": 843, "Region_92": 860, "Region_292": 710, "Region_350": 484, "Region_720": 1533, "Region_192": 919, "Region_511": 1220, "Region_258": 801, "Region_298": 771, "Region_142": 692, "Region_432": 671, "Region_477": 1001, "Region_22": 611, "Region_267": 764, "Region_355": 681, "Region_730": 1077, "Region_123": 937, "Region_172": 30, "Region_173": 731, "Region_304": 451, "Region_523": 664, "Region_442": 693, "Region_275": 788, "Region_590": 708, "Region_5": 436, "Region_47": 1165, "Region_181": 864, "Region_76": 678, "Region_88": 836, "Region_208": 469, "Region_366": 1733, "Region_281": 642, "Region_405": 805, "Region_499": 685, "Region_334": 772, "Region_44": 1667, "Region_32": 683, "Region_438": 621, "Region_43": 766, "Region_464": 465, "Region_466": 691, "Region_645": 696, "Region_386": 753, "Region_365": 1461, "Region_614": 797, "Region_656": 1118, "Region_384": 637, "Region_626": 500, "Region_738": 1056, "Region_495": 703, "Region_726": 721, "Region_319": 753, "Region_743": 1196, "Region_74": 299, "Region_278": 322, "Region_104": 1381, "Region_565": 1218, "Region_482": 904, "Region_86": 775, "Region_224": 843, "Region_613": 1016, "Region_441": 861, "Region_731": 2127, "Region_190": 924, "Region_578": 879, "Region_14": 707, "Region_348": 690, "Region_456": 1750, "Region_144": 924, "Region_273": 545, "Region_718": 771, "Region_24": 730, "Region_457": 787, "Region_325": 669, "Region_193": 1257, "Region_698": 667, "Region_445": 1476, "Region_707": 1107, "Region_606": 1446, "Region_416": 639, "Region_426": 648, "Region_436": 628, "Region_701": 679, "Region_218": 1048, "Region_489": 965, "Region_65": 666, "Region_690": 1226, "Region_81": 750, "Region_212": 852, "Region_66": 653, "Region_406": 283, "Region_36": 407, "Region_654": 838, "Region_60": 688, "Region_189": 723, "Region_367": 792, "Region_502": 671, "Region_293": 1192, "Region_422": 646, "Region_637": 760, "Region_479": 751, "Region_639": 782, "Region_660": 431, "Region_222": 713, "Region_513": 595, "Region_723": 1075, "Region_756": 1008, "Region_253": 593, "Region_185": 846, "Region_737": 1037, "Region_587": 802, "Region_739": 579, "Region_612": 1233, "Region_671": 848, "Region_449": 666, "Region_17": 683, "Region_638": 732, "Region_665": 512, "Region_41": 668, "Region_461": 848, "Region_201": 756, "Region_229": 1012, "Region_52": 428, "Region_408": 579, "Region_217": 770, "Region_363": 792, "Region_559": 1014, "Region_608": 1237, "Region_500": 749, "Region_611": 451, "Region_220": 743, "Region_452": 672, "Region_20": 748, "Region_648": 783, "Region_197": 635, "Region_103": 625, "Region_425": 613, "Region_240": 1309, "Region_722": 1310, "Region_196": 1292, "Region_283": 727, "Region_297": 1256, "Region_659": 771, "Region_322": 718, "Region_512": 702, "Region_577": 734, "Region_667": 638, "Region_336": 625, "Region_556": 666, "Region_692": 1112, "Region_72": 275, "Region_244": 588, "Region_485": 738, "Region_311": 1638, "Region_10": 778, "Region_686": 694, "Region_585": 625, "Region_294": 900, "Region_155": 730, "Region_417": 679, "Region_149": 205, "Region_552": 596, "Region_531": 589, "Region_732": 2027, "Region_154": 579, "Region_538": 546, "Region_19": 688, "Region_407": 663, "Region_401": 676, "Region_368": 811, "Region_721": 1365, "Region_696": 751, "Region_101": 698, "Region_223": 1028, "Region_748": 706, "Region_537": 843, "Region_750": 1070, "Region_70": 2543, "Region_230": 701, "Region_338": 705, "Region_174": 663, "Region_514": 622, "Region_644": 653, "Region_305": 1072, "Region_358": 668, "Region_370": 767, "Region_238": 515, "Region_383": 746, "Region_122": 1002, "Region_345": 548, "Region_517": 779, "Region_195": 911, "Region_117": 815, "Region_306": 1403, "Region_143": 780, "Region_507": 568, "Region_476": 812, "Region_505": 702, "Region_30": 386, "Region_666": 650, "Region_249": 665, "Region_356": 601, "Region_598": 1079, "Region_308": 1593, "Region_524": 584, "Region_516": 665, "Region_597": 707, "Region_105": 717, "Region_93": 807, "Region_641": 1214, "Region_746": 755, "Region_716": 1415, "Region_446": 925, "Region_213": 1119, "Region_440": 854, "Region_320": 659, "Region_674": 832, "Region_741": 765, "Region_39": 668, "Region_349": 681, "Region_633": 899, "Region_49": 527, "Region_385": 735, "Region_600": 773, "Region_558": 930, "Region_13": 810, "Region_599": 830, "Region_491": 759, "Region_337": 656, "Region_463": 1242, "Region_678": 734, "Region_120": 523, "Region_214": 1661, "Region_328": 425, "Region_740": 853, "Region_382": 844, "Region_484": 1189, "Region_162": 568, "Region_146": 821, "Region_699": 1181, "Region_594": 204, "Region_619": 99, "Region_503": 771, "Region_535": 769, "Region_342": 713, "Region_415": 703, "Region_724": 724, "Region_717": 1362, "Region_673": 844, "Region_423": 664, "Region_610": 1499, "Region_347": 717, "Region_646": 811, "Region_186": 895, "Region_137": 714, "Region_555": 206, "Region_4": 670, "Region_745": 791, "Region_156": 864, "Region_48": 667, "Region_78": 759, "Region_80": 786, "Region_16": 249, "Region_601": 787, "Region_414": 743, "Region_687": 531, "Region_711": 931, "Region_177": 1024, "Region_87": 565, "Region_357": 736, "Region_158": 1046, "Region_71": 1600, "Region_138": 688, "Region_313": 1453, "Region_584": 685, "Region_504": 590, "Region_727": 733, "Region_300": 632, "Region_314": 933, "Region_359": 573, "Region_274": 739, "Region_107": 262, "Region_607": 941, "Region_99": 998, "Region_427": 664, "Region_650": 826, "Region_467": 1280, "Region_194": 1166, "Region_351": 709, "Region_693": 792, "Region_715": 835, "Region_695": 903, "Region_753": 962, "Region_493": 914, "Region_178": 566, "Region_7": 916, "Region_595": 694, "Region_170": 715, "Region_481": 826, "Region_205": 965, "Region_264": 941, "Region_169": 794, "Region_528": 380, "Region_109": 549, "Region_704": 636, "Region_256": 697, "Region_679": 804, "Region_533": 733, "Region_539": 819, "Region_98": 726, "Region_202": 633, "Region_316": 599, "Region_471": 794, "Region_206": 916, "Region_388": 841, "Region_631": 785, "Region_632": 429, "Region_560": 610, "Region_295": 743, "Region_478": 1052, "Region_672": 898, "Region_685": 309, "Region_321": 669, "Region_8": 614, "Region_33": 853, "Region_728": 895, "Region_28": 673, "Region_553": 701, "Region_563": 843, "Region_589": 203, "Region_709": 700, "Region_21": 609, "Region_387": 324, "Region_643": 740, "Region_332": 698, "Region_483": 662, "Region_216": 1372, "Region_579": 701, "Region_262": 664, "Region_691": 643, "Region_12": 655, "Region_494": 261, "Region_373": 716, "Region_111": 721, "Region_163": 1138, "Region_534": 8, "Region_605": 1110, "Region_564": 536, "Region_245": 628, "Region_237": 600, "Region_102": 700, "Region_226": 756, "Region_569": 311, "Region_472": 461, "Region_63": 672, "Region_67": 556, "Region_260": 640, "Region_622": 591, "Region_708": 941, "Region_279": 705, "Region_411": 622, "Region_530": 733, "Region_231": 751, "Region_729": 1448, "Region_458": 1533, "Region_84": 766, "Region_341": 735, "Region_344": 717, "Region_488": 639, "Region_64": 646, "Region_574": 703, "Region_79": 700, "Region_198": 460, "Region_430": 468, "Region_369": 1191, "Region_34": 587, "Region_649": 1044, "Region_153": 902, "Region_447": 676, "Region_291": 736, "Region_515": 179, "Region_157": 1059, "Region_508": 809, "Region_658": 551, "Region_27": 1026, "Region_215": 1179, "Region_431": 634, "Region_492": 677, "Region_329": 781, "Region_465": 488, "Region_110": 812, "Region_346": 718, "Region_125": 991, "Region_742": 800, "Region_497": 551, "Region_653": 772, "Region_23": 692, "Region_609": 1158, "Region_335": 488, "Region_393": 843, "Region_167": 841, "Region_618": 717, "Region_443": 867, "Region_455": 1185, "Region_576": 631, "Region_410": 656, "Region_128": 315, "Region_719": 1281, "Region_112": 707, "Region_234": 760, "Region_55": 680, "Region_669": 922, "Region_58": 653, "Region_203": 801, "Region_460": 758, "Region_115": 741, "Region_106": 764, "Region_183": 845, "Region_287": 779, "Region_655": 905, "Region_418": 782, "Region_210": 760, "Region_362": 740, "Region_184": 1052, "Region_755": 776, "Region_486": 691, "Region_710": 851, "Region_617": 268, "Region_744": 1013, "Region_642": 512, "Region_327": 728, "Region_371": 603, "Region_462": 742, "Region_57": 589, "Region_254": 609, "Region_683": 325, "Region_126": 626, "Region_433": 720, "Region_340": 742, "Region_470": 781, "Region_603": 577, "Region_310": 1065, "Region_175": 2143, "Region_413": 651, "Region_54": 662, "Region_11": 819, "Region_272": 679, "Region_706": 1909, "Region_580": 680, "Region_670": 554, "Region_444": 643, "Region_56": 638, "Region_428": 640, "Region_420": 741, "Region_284": 504, "Region_487": 1580, "Region_204": 693, "Region_378": 738, "Region_636": 894, "Region_586": 633, "Region_700": 734, "Region_734": 748, "Region_118": 705, "Region_191": 768, "Region_259": 840, "Region_527": 399, "Region_361": 529, "Region_130": 701, "Region_549": 526, "Region_448": 768, "Region_404": 659, "Region_377": 725, "Region_630": 409, "Region_180": 1133, "Region_392": 686, "Region_247": 705, "Region_398": 674, "Region_324": 783, "Region_498": 720, "Region_235": 651, "Region_705": 858, "Region_581": 738, "Region_91": 283, "Region_394": 771, "Region_141": 673, "Region_151": 1043, "Region_83": 517, "Region_469": 1397, "Region_331": 530, "Region_450": 658, "Region_490": 667, "Region_379": 888, "Region_374": 611, "Region_677": 710, "Region_59": 652, "Region_165": 843, "Region_42": 518, "Region_250": 353, "Region_525": 1015, "Region_554": 217, "Region_140": 716, "Region_108": 725, "Region_150": 559, "Region_343": 598, "Region_152": 429, "Region_18": 727, "Region_318": 688, "Region_233": 670, "Region_282": 706, "Region_575": 1070, "Region_25": 651, "Region_496": 688, "Region_453": 858, "Region_73": 428, "Region_100": 952, "Region_439": 783, "Region_354": 725, "Region_543": 312, "Region_168": 735, "Region_326": 664, "Region_252": 575, "Region_94": 403, "Region_209": 701, "Region_676": 1017, "Region_225": 670, "Region_475": 795, "Region_211": 866, "Region_390": 721, "Region_251": 702, "Region_9": 927, "Region_228": 852, "Region_37": 358, "Region_302": 747, "Region_62": 707, "Region_38": 497, "Region_424": 725, "Region_263": 606, "Region_506": 635, "Region_375": 657, "Region_409": 450, "Region_624": 681, "Region_45": 752, "Region_402": 655, "Region_571": 337, "Region_399": 707, "Region_546": 325, "Region_570": 731, "Region_396": 816, "Region_85": 759, "Region_219": 824, "Region_116": 1082, "Region_652": 567, "Region_380": 698, "Region_582": 604, "Region_604": 799, "Region_77": 413, "Region_330": 687, "Region_360": 436, "Region_459": 660, "Region_75": 330, "Region_468": 676, "Region_199": 723, "Region_542": 384, "Region_712": 759, "Region_133": 13, "Region_132": 683, "Region_429": 311, "Region_35": 483, "Region_129": 858, "Region_501": 675, "Region_681": 517, "Region_684": 480, "Region_647": 88, "Region_735": 470, "Region_403": 747, "Region_434": 738, "Region_124": 902, "Region_664": 446, "Region_159": 474, "Region_545": 277, "Region_682": 640, "Region_697": 1141, "Region_688": 564, "Region_166": 724, "Region_751": 659, "Region_289": 598, "Region_372": 707, "Region_97": 713, "Region_290": 279, "Region_246": 626, "Region_668": 713, "Region_400": 728, "Region_702": 655, "Region_82": 116, "Region_509": 281, "Region_255": 704, "Region_333": 595, "Region_89": 744, "Region_412": 671, "Region_207": 664, "Region_227": 676, "Region_747": 752, "Region_29": 723, "Region_200": 741, "Region_339": 786, "Region_364": 793, "Region_50": 648, "Region_271": 752, "Region_164": 740, "Region_161": 926, "Region_551": 442, "Region_675": 764, "Region_317": 662, "Region_566": 202, "Region_435": 655, "Region_680": 784, "Region_40": 473, "Region_248": 715, "Region_114": 650, "Region_239": 704, "Region_615": 791, "Region_266": 685, "Region_301": 795, "Region_421": 402, "Region_236": 336, "Region_713": 885, "Region_312": 354, "Region_733": 403, "Region_243": 674, "Region_419": 449, "Region_757": 523, "Region_714": 776, "Region_307": 400, "Region_376": 472, "Region_567": 605, "Region_134": 275, "Region_68": 594, "Region_657": 221, "Region_285": 221, "Region_265": 792, "Region_241": 626, "Region_15": 708, "Region_568": 624, "Region_635": 726, "Region_662": 188, "Region_147": 260, "Region_182": 219, "Region_573": 345, "Region_257": 482, "Region_113": 678, "Region_96": 24, "Region_53": 739, "Region_51": 138, "Region_752": 368, "Region_437": 355, "Region_389": 214, "Region_663": 345, "Region_651": 235, "Region_548": 18, "Region_529": 13, "Region_561": 99, "Region_352": 614, "Region_187": 62, "Region_119": 11, "Region_627": 600, "Region_121": 35, "Region_171": 9, "Region_269": 43, "Region_276": 27, "Region_145": 26, "Region_139": 16, "Region_592": 7, "Region_623": 153, "Region_510": 22, "Region_6": 2, "Region_550": 11, "Region_280": 21, "Region_176": 20, "Region_540": 17, "Region_26": 6, "Region_518": 7, "Region_520": 7, "Region_303": 34, "Region_591": 119, "Region_532": 19, "Region_593": 7, "Region_1": 1, "Region_160": 18, "Region_557": 4, "Region_521": 7, "Region_473": 15, "Region_588": 10, "Region_541": 17, "Region_135": 19, "Region_131": 10, "Region_736": 1, "Region_136": 17, "Region_90": 13, "Region_277": 40, "Region_547": 18, "Region_522": 3, "Region_754": 5, "Region_629": 3, "Region_544": 6, "Region_628": 3, "Region_616": 1, "Region_536": 10, "Region_288": 1, "Region_519": 7, "Region_2": 3, "Region_620": 7, "Region_625": 3, "Region_69": 5, "Region_621": 5, "Region_3": 4, "Region_703": 2, "Region_572": 3, "Region_725": 1, "Region_526": 3, "Region_562": 1}}, "feature_anomaly_detection": {"statistical_anomalies": {"n_pois": {"count": 19, "percentage": 2.509907529722589, "max_z_score": 7.6587756010922785, "anomaly_indices": [43, 69, 70, 174, 213, 285, 298, 307, 310, 312, 365, 455, 457, 486, 609, 705, 719, 730, 731]}, "pop": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "edu": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "income": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "unemploy": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "cd": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "crime": {"count": 0, "percentage": 0.0, "max_z_score": NaN, "anomaly_indices": []}, "energy": {"count": 3, "percentage": 0.3963011889035667, "max_z_score": 3.443756329478822, "anomaly_indices": [174, 285, 367]}}, "isolation_forest": {"anomaly_count": 76, "anomaly_percentage": 10.039630118890356, "anomaly_indices": "{640, 3, 132, 515, 517, 518, 519, 520, 524, 728, 655, 668, 285, 296, 298, 43, 174, 688, 305, 689, 307, 564, 309, 310, 691, 312, 696, 698, 699, 444, 700, 702, 703, 192, 705, 195, 69, 70, 455, 457, 715, 716, 462, 718, 719, 720, 466, 721, 212, 213, 468, 215, 597, 89, 90, 598, 604, 605, 94, 607, 608, 609, 730, 611, 731, 486, 103, 619, 364, 365, 620, 239, 367, 624, 722, 510}"}, "dbscan_clustering": {"n_clusters": 1, "noise_count": 15, "noise_percentage": 1.9815059445178336, "anomaly_indices": "{705, 611, 605, 69, 455, 296, 457, 716, 174, 367, 728, 730, 731, 668, 285}"}, "consistent_anomalies": {"count": 9, "indices": ["705", "69", "455", "457", "174", "367", "730", "731", "285"], "region_ids": ["706", "70", "456", "458", "175", "368", "731", "732", "286"]}, "high_correlation_pairs": [], "feature_matrix_shape": [757, 8], "valid_region_count": 757}, "quality_analysis": {"energy_quality": {"total_regions": 757, "valid_energy_count": 757, "missing_energy_count": 0, "zero_energy_count": 10, "energy_range": [0.0, 2.439598218078459], "energy_mean": 0.9950277696097821, "energy_std": 0.419475221316631, "outlier_count": 24, "outlier_percentage": 3.1704095112285335, "energy_types": {"float": 747, "int": 10}}, "missing_data": {}, "outliers": {}, "data_types": {}}, "distribution_analysis": {"train": {"count": 605, "mean": 1.0011807094696834, "std": 0.4278227822163968, "min": 0.0, "max": 2.439598218078459, "median": 0.9880231825494366, "q25": 0.7513158882585057, "q75": 1.2123750788086154, "values": [1.1832614772684686, 0.898018738041985, 1.4841460826024866, 0.9971988142310972, 1.3453049937466355, 0.7333971540072284, 1.1979207615677268, 1.1547336785889932, 0.9278062127656275, 1.0629611964695835, 1.0662546399527661, 0.7088082936601423, 0.8805594452188155, 0.4385915271612816, 1.113601784392119, 0.9653114228928004, 1.0878340536513704, 0.9352319083516097, 1.2214184196252629, 0.6784263566018837, 1.0207816426788248, 1.0509272299791694, 0.975436119724905, 1.0849930612522483, 0.5969984093544973, 1.2606987721570044, 0.9681144830784062, 2.289265684310572, 0.9290806936637203, 2.337991012251715, 1.192379352323566, 1.0849433120299024, 0.5934948208891718, 0.7517689435888886, 0.8179362498886796, 0.9059742512270554, 0.9092860218196643, 1.4290250795654096, 1.7999160481553014, 1.8848345007115392, 0.0, 1.115438757978674, 1.0190899458305684, 1.377999852861643, 1.1618683636802916, 0.8005312216811887, 0.6000904424559476, 0.8193117347301896, 1.218176357100158, 0.41138949993132157, 1.0180193840750495, 0.6776697237350686, 0.6456476137726832, 1.0813968367425923, 0.6212100199232609, 0.8001567893909215, 1.0574589822643825, 0.9377831471614287, 1.3244668687938133, 0.8647944989036673, 0.8446273780151633, 0.4982401420657393, 0.6507170167447136, 0.4765365630301671, 1.092346288743819, 0.8585357972766724, 0.9587881856304541, 1.2940949023055717, 0.7513158882585057, 2.2108875551634326, 0.9520145395196397, 0.7073379275803319, 0.4243572857611724, 1.0760095239653806, 0.8913838155845767, 0.9327076088991495, 0.9824148328154032, 1.3399296812071613, 1.7288810066314177, 0.5263238894132236, 1.0551639075049013, 0.36771254622181354, 1.2976593747617367, 1.1679522267164317, 1.6896762946940884, 0.7363984003544496, 1.6106085325788082, 0.7828529660579104, 0.7775352452838397, 0.7506346237158891, 1.3983846608603148, 1.0136419436208255, 1.3061171374014249, 1.1606868016300076, 0.9172046188674493, 0.26527485622740815, 1.0845530146045261, 0.8426486607495954, 0.957624919312099, 0.8373355579349422, 1.1619650863321895, 0.9196142304754875, 1.1971029509603812, 0.6648977935912279, 0.8711486499312437, 0.4734365436456468, 0.45967351908067294, 1.147462029698477, 0.08419510744533082, 0.9014004739868523, 0.7032146283513601, 1.7214992193992393, 0.37157062264273044, 0.1897716305462591, 0.724350894522708, 1.0976899166002048, 1.0153996889175314, 0.7265421582671018, 1.3117277753197896, 1.0167690458048138, 0.551944049507956, 0.4850109819736627, 0.9288490927635457, 1.1688073366035467, 0.5556175489612444, 0.46872599099402107, 1.1409239034005991, 1.1513523127587475, 0.8635733091406744, 1.5642869322603676, 1.8953371844651905, 1.8766261385237466, 0.978347868964855, 0.816531127734521, 1.2457464655861705, 0.7233572655561289, 1.309021035057202, 1.0800088107685086, 0.9446986002181085, 1.013140921193072, 1.1062241797275654, 0.9356946071207937, 0.9710000826188256, 0.9127250326154939, 0.7791890938252614, 0.0, 0.9290315812356734, 1.7593515473450385, 1.14859411464504, 0.8715176791818666, 0.0, 0.7415830090647516, 1.408376046497607, 1.4490555887467929, 0.41154087777936166, 0.660392685609597, 1.0681845890625843, 1.987518421029838, 0.9301440740249578, 1.686998421742258, 1.5542588096793002, 0.7709709231181622, 0.3691442665502659, 0.7925124713134933, 0.8046378614838168, 0.8159395299577773, 1.1993888694279626, 1.8992223842102225, 0.8080924873281886, 0.17068339634754076, 1.222919253019312, 0.7063796915704785, 1.094358320661902, 0.4778637045717106, 1.0668256801373956, 0.6414659344804665, 0.982901580921048, 0.16313063728300659, 1.0390587135034774, 0.0, 0.9351177435886872, 0.510407253648586, 0.6635649528803791, 0.876016118874107, 0.47171357514944523, 0.6333313475644187, 1.5593427873620627, 0.6910259476240891, 1.124368625554801, 1.1870478912879383, 1.173854506516275, 0.799755337798307, 1.6009840426383488, 0.5107015755655134, 1.0985631834527096, 0.6896485500336117, 1.1916410306105718, 1.1378977780903337, 1.3064661999326888, 1.0242959393862552, 0.911941909480981, 1.07274481230075, 1.0328338153164018, 1.8155648915362759, 0.9005339139172575, 0.9283907523626993, 1.5869008171179086, 0.49818557210193876, 0.1902050720568096, 1.0539231672400418, 1.0856241560889381, 1.3805026902078723, 0.9020072533479715, 0.1470379982142391, 1.1263193521297965, 0.6527130913824701, 0.9538721530517325, 1.9110823577731595, 1.1433478932973369, 1.1329840131527509, 0.23653018963783506, 0.14665796549305843, 0.576500211674429, 0.8146464757437841, 1.212566389065705, 0.7945217623688772, 0.9230968297800205, 1.201122813206592, 1.6386192427852735, 0.963880902626178, 1.1094289659212138, 1.0891440233358938, 1.537549687918335, 0.5152576187478394, 0.25615549047447017, 1.1421755212725624, 1.3065349419474075, 0.3613330430898962, 1.112044686557654, 0.8747536645151797, 1.9325887437940963, 0.8821982268689014, 1.7590153469322987, 1.3440948152621528, 0.9527434236094072, 1.1323890383900443, 1.6208676272345153, 1.2066120816181398, 1.0743266938620106, 1.3351469377265974, 1.1655022700570732, 1.2722318897048428, 1.6589522014810638, 1.1616287233586104, 0.766452874018715, 1.073525606785137, 0.35468262535354256, 0.8368655874033725, 0.7077411852852131, 1.0142125061098282, 1.3891288101807793, 1.2170128783727496, 0.8913191555854382, 0.0, 1.0326999485142099, 0.5799774896550028, 0.05992161873992977, 1.2896982605392755, 0.22754957551951704, 0.7448224879692691, 1.8855953980916067, 0.9597679156114524, 0.7492870662177005, 1.1551677327172618, 0.9285426896827395, 1.1662089369813144, 1.7077167157872524, 1.3166198013347512, 0.6124352875458057, 0.9566732698443513, 0.3510552195434595, 0.6852015960155381, 1.6667280134661626, 0.577823010990117, 1.523682231634592, 1.104563012886036, 0.9190853515685463, 0.8682792289815681, 1.006760631466999, 0.897813132373824, 0.4434820062325024, 0.8030338084064189, 0.8988490470362632, 0.9320441196783705, 0.5822392212622356, 0.46844108292442294, 1.347624086083418, 1.1881822509701665, 0.32771985361709, 1.01560356384115, 0.0, 0.6794511846896026, 1.4190078611184127, 1.145790741045359, 0.9285036373150872, 0.7623096157283373, 1.5041503700373684, 0.8928000040629419, 0.9269158941104148, 1.0592367290564482, 1.1829299078975688, 0.6504778204292114, 1.6179891111386169, 0.9406053591738326, 0.9400630020034518, 0.9841650816888368, 0.6215808397790887, 0.22111678325291403, 1.5714692863503241, 0.2256358753237548, 1.0054521090237372, 0.9118725833939405, 1.4249127359645808, 1.0460926083944964, 0.6805392742987182, 1.018344330396491, 1.0883404664467882, 0.782091950471126, 0.33113439787165083, 0.9316858277608342, 0.8615071486615598, 0.23525684681895112, 0.4320951674138675, 0.9903254792516878, 0.9329403125825395, 0.5466965690340866, 1.274345515144166, 1.8048635647487075, 1.0102851423032548, 1.5241054928012967, 1.287949146881846, 0.9106577563361249, 1.6239117480919276, 1.700780928750186, 0.264365759998004, 0.624399265664264, 1.4884913879156942, 1.0487674449981623, 0.6915976073189507, 0.5675139374900177, 0.9125801139359451, 1.0329790944147945, 2.1101615319170137, 2.1377769932116895, 1.576158608721789, 0.7979150234472889, 0.8340384246105065, 1.6803958131337449, 0.6197629736377539, 1.1152042523963626, 0.781334783072716, 1.0881656962896975, 0.9114773113606152, 0.8204313851103185, 0.7994163923611236, 0.9075714065406586, 1.1595075082934203, 0.9880231825494366, 2.094322605012628, 1.6680325683158748, 1.7657314574953618, 1.0465918679870763, 0.9086619995022968, 1.3969895608505907, 1.2284582413169343, 1.0332252690415262, 1.0903116494746294, 1.2917662899640572, 1.7717405487187612, 1.0714539417055633, 1.1256340535702212, 1.1531472671855802, 0.9649449896351012, 0.8365908799271359, 1.0946491730099743, 1.1088945035117022, 1.027983818329188, 1.2359390203373135, 1.4073364246728008, 1.036336231723425, 1.2793448295464382, 1.1207379795646222, 1.2547004709396044, 1.4405604010991302, 0.6846008515152946, 1.8778984372509742, 0.9552725608362626, 1.371574178907148, 1.1410420790209825, 1.8568445558051039, 0.9786694465395271, 0.4410321550085986, 1.8317809830844691, 1.4271657674877383, 0.9769380263376989, 0.34817273894871736, 1.3094070759158944, 1.8654026929549645, 0.23296323399823252, 0.0, 0.8185537865636825, 0.905863315880927, 0.7821811153335327, 1.1136601764777805, 1.1408406969726503, 1.0076557968619226, 0.5229042833518818, 1.5816749746422805, 0.9157166629905734, 1.291254283354212, 0.571822527940548, 0.7506481875757003, 0.8894617932475574, 0.46599059512511576, 1.5423387540640925, 1.338220376071025, 1.1250979239408492, 1.8616642813642663, 1.0196775880613873, 1.1060127521440215, 1.1588972381377887, 0.7659221228704618, 0.9046312104800303, 1.7092286493033415, 1.4624151052240335, 1.4125068364096862, 1.8024364508836979, 1.7030537103201977, 1.7322687139092654, 1.6399761126806114, 0.32454510195574315, 1.0919746132402635, 0.1967768361788318, 0.22238481627270282, 0.7921853167247808, 1.13805236493733, 0.13170274656918818, 1.6575624956984867, 1.061693886121196, 0.5920985281312026, 0.36482650502092584, 0.6863844508732154, 1.0600490827717317, 0.8112241636249875, 0.3942880582751397, 1.1792660688549563, 0.31439232623854246, 1.1465139171039562, 0.5993691250789346, 0.9254869577339372, 0.8381586917577989, 1.088202053938602, 0.36273462723297023, 1.1940592479244474, 1.0899957705268302, 2.439598218078459, 1.0497190818207807, 1.2210859128445517, 1.54685412569607, 0.6821936173386678, 1.5169438790036422, 0.820475822816225, 0.3564895862916338, 1.7499084400802594, 1.233263157712693, 0.7600918620080177, 0.8083507711779737, 0.9639787967194832, 1.0981542213679345, 1.3528592920266758, 0.9446347721352447, 1.0990297952394716, 1.1369287400952388, 1.1480518503078188, 0.6268536603641319, 1.3087200986139278, 0.8814187697267871, 1.1481393274359992, 1.2408540598399382, 1.645860357672865, 1.427010479331858, 1.4558918533965384, 0.23363344641607042, 0.7784740206292863, 1.2770861604920853, 0.9553383821938192, 1.4620891113489376, 0.8149851131946847, 1.7250291336187922, 1.730126729599214, 0.78708865812147, 1.999174620523051, 0.8123288141077365, 1.614948502568956, 1.9903985691776738, 1.402359441486768, 0.4902365136875413, 1.924805962630905, 0.9321437610090355, 0.6979914419244394, 0.701953598132674, 1.2091883960812757, 0.6971359771017479, 0.5898644521595392, 0.7968015770536161, 0.9299995896400455, 0.9046840826834734, 0.43844783154092015, 1.5630616970439788, 0.9023856717807847, 1.1478120620412355, 0.8369223673188277, 1.0131003395259293, 0.7010829313885616, 0.9707241100467544, 1.438286415482738, 0.7279961016544171, 0.9210732627702902, 0.18210542696334053, 0.6641688599268726, 0.1454067205024678, 0.47587931743221396, 1.1278921632774248, 1.11016279706167, 1.1052962652009968, 0.8513739507180875, 0.3982446403763661, 0.8156526531665197, 0.720321768073092, 1.3520210289919112, 0.8652440031591063, 1.3214496571449719, 0.5554984165897093, 1.2673870276532295, 1.252631873967636, 1.159421355571475, 0.6715592957985755, 1.288259491419164, 0.5580183357462403, 0.7527570209755677, 0.8391620589365392, 0.32870333939972224, 0.919941008187782, 1.6173737328694606, 1.0289922662493864, 1.6738533008957504, 0.9913542998143011, 1.1246278902856275, 1.0909540614586606, 0.7593076118782209, 1.599567040229682, 1.0495884028992333, 1.0197841084260866, 1.7387159788741666, 0.9160930243722248, 1.1577180318271383, 1.1496593213707502, 0.9934883750700849, 1.3401128138463876, 1.031400376806251, 0.8798774892994978, 0.613623249352815, 0.09626843980015548, 0.5194887056569519, 0.946208226973767, 1.8550988760315406, 0.8764370989092602, 1.8116967220883395, 0.7464039452829913, 0.7270970591752347, 0.6013682412566989, 0.15421975905145496, 0.803016211782207, 0.0, 1.2525682459631278, 1.0475192623318752, 0.7556387788518338, 1.1713183535107792, 0.7289911212184698, 0.9596698398438095, 1.1100502125444378, 1.432080882287103, 1.073076890784993, 1.2123750788086154, 1.11885434633055, 0.689046846154849, 1.3093924203557317, 1.0279263938728773, 1.0353084544642603, 0.9969687901411125, 1.335211995667853, 0.9539253558951211, 0.0, 0.44222701926608443, 1.242751494932059]}, "val": {"count": 75, "mean": 0.9811455679016492, "std": 0.3928110770869038, "min": 0.0, "max": 1.821865489072648, "median": 0.9989310960250134, "q25": 0.7857459072725315, "q75": 1.1754518193982115, "values": [0.5693023572081812, 1.331429806749735, 1.6190427717646128, 0.734644221874063, 1.2608600602760989, 1.821865489072648, 1.159122731552078, 1.139138499439985, 1.1579725646243282, 0.9445633810676305, 0.22666409100863955, 0.4941171866856136, 0.4850898083758863, 0.47015510078559236, 0.31423010138828017, 1.2855199224440816, 1.0397848304843609, 1.4437378693703198, 0.4384424171391942, 0.7277398069928256, 0.8620474722231005, 0.9989310960250134, 1.4177434832929583, 0.5617343159645422, 1.1201141685898812, 0.8646840473091777, 0.904363708552338, 1.1339471663907215, 1.0597226325242877, 1.4590117150278084, 0.30345567969410314, 1.1426797922794996, 1.130204755054245, 0.39454540525756887, 1.716051219712979, 0.8500605175561065, 0.9846069056726092, 0.8553276439118739, 0.9602692538359551, 1.1704195407414968, 1.007851917951237, 0.5159107853184822, 0.589611507921287, 1.5389786554206166, 1.1666284360250219, 0.9403126390670576, 1.102414602390568, 1.2448281593780284, 0.7911421680580465, 0.8780123218708937, 0.1556809002701158, 1.171448276236578, 0.540445372397693, 1.179455362559845, 0.9223016392762019, 1.1479448300403436, 1.0761715365944209, 1.4708538188034375, 0.0, 0.7884436105940223, 0.7935499491690927, 0.7830482039510405, 0.5571595254336056, 1.0505818678095038, 0.9208120722014297, 1.1972336729599506, 1.433148232243942, 1.066607871394811, 0.8629887867248988, 1.2808093057440602, 0.86194238351077, 1.7900025980918315, 1.7699149113899206, 0.8542422097271251, 1.58209592417739]}, "test": {"count": 77, "mean": 0.9602048671795195, "std": 0.37387891398520506, "min": 0.09397037264876167, "max": 1.6891620107789322, "median": 0.9871774957966934, "q25": 0.7789327418253481, "q75": 1.2450442453285357, "values": [0.8171730989724755, 0.6219341340641154, 0.7898518433876663, 1.1358468234523555, 1.2450442453285357, 1.0205841079253986, 0.24408153530112112, 1.340012302074912, 0.09397037264876167, 1.181227036980676, 0.12832795314925655, 0.8756395004293335, 1.2702165505901113, 0.5425000538638084, 0.41702738598427, 0.8196146127534669, 1.6325679991973123, 0.9978269838726005, 1.3922242101905091, 0.9869998309811239, 1.08064432787699, 1.3652949906525118, 0.7908103865367117, 1.0265746496604826, 1.0345167728432285, 1.0073673039196713, 1.2188149573923956, 1.55515287243428, 0.8893751930751675, 0.5894093490658201, 0.8986938408647529, 1.0630000531611574, 1.1044129791503188, 0.6510426499977723, 1.2483019937011905, 1.187264331837397, 1.0196983045306316, 0.5157779232149826, 1.1337417747239527, 0.2418461038478568, 1.4713939237800737, 0.9871774957966934, 1.2937636102239707, 0.7272509085078025, 1.396531840426333, 0.485570902024, 0.8564710387478045, 0.44061419440540645, 1.5374673302148079, 1.3245050917502517, 1.264021434808807, 0.9199554393498143, 1.4210242369948498, 1.1315353195357738, 0.9360992218566443, 0.7789327418253481, 1.047409415301666, 1.039057943082103, 0.8650850013446107, 0.3180106938604738, 0.9110467453447088, 1.6145323427139282, 0.979425896638511, 0.7287786883812721, 1.253998957522327, 0.9042567742966409, 1.6891620107789322, 1.493219813925097, 1.1515856230918533, 0.6080134730754221, 0.8472622734671509, 0.20236286587170021, 0.35089156278364664, 0.5382025854238416, 1.4299904451405645, 0.9014949026104971, 0.9152606882846106]}}, "image_availability": {"status": "skipped", "reason": "image directory not configured"}, "diagnosis": {"total_issues": 2, "issues": ["知识图谱连通性差，最大连通分量仅占0.4%", "发现15个区域连接度异常低"], "recommendations": ["检查知识图谱构建过程，增加实体间的关联关系", "检查连接度异常低的区域，补充相关实体关系", "先运行模型训练生成预测结果"], "priority_recommendations": ["中等优先级：完善知识图谱结构"]}}