import torch
import torch.nn as nn
import torch.nn.functional as F
import copy
from models.graph_encoder import GraphEncoder

class BGRL(nn.Module):
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(BGRL, self).__init__()
        self.g = kwargs['g']
        self.tau = 0.99  # EMA更新率
        
        # 在线编码器
        self.online_encoder = GraphEncoder(node_emb, rel_emb, layer_sizes, dropout_list)
        self.online_predictor = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.BatchNorm1d(layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], 64)
        )
        
        # 目标编码器
        self.target_encoder = GraphEncoder(node_emb, rel_emb, layer_sizes, dropout_list)
        # 目标编码器不需要梯度
        for param in self.target_encoder.parameters():
            param.requires_grad = False
        
        # 初始化目标编码器
        self._copy_weights()
    
    def _copy_weights(self):
        """复制在线编码器权重到目标编码器"""
        for online_param, target_param in zip(
            self.online_encoder.parameters(), self.target_encoder.parameters()
        ):
            target_param.data.copy_(online_param.data)
    
    def _update_target_network(self):
        """使用EMA更新目标网络"""
        for online_param, target_param in zip(
            self.online_encoder.parameters(), self.target_encoder.parameters()
        ):
            target_param.data = self.tau * target_param.data + (1 - self.tau) * online_param.data
    
    def _augment_graph(self, g):
        """增强图的简单实现"""
        # 在实际应用中，这里可以进行边掩码、特征扰动等操作
        # 当前简化实现，直接返回原图
        return g
    
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        # 创建两个图视图
        g1 = self._augment_graph(self.g)
        g2 = self._augment_graph(self.g)
        
        # 在线网络前向传播（视图1）
        online_feat1 = self.online_encoder(g1, node_idx)
        online_pred1 = self.online_predictor(online_feat1)
        
        # 在线网络前向传播（视图2）
        online_feat2 = self.online_encoder(g2, node_idx)
        online_pred2 = self.online_predictor(online_feat2)
        
        # 目标网络前向传播（无梯度）
        with torch.no_grad():
            target_feat1 = self.target_encoder(g1, node_idx)
            target_feat2 = self.target_encoder(g2, node_idx)
        
        # 计算损失（在线预测器预测目标特征）
        loss1 = self._byol_loss(online_pred1, target_feat2)
        loss2 = self._byol_loss(online_pred2, target_feat1)
        
        # 总损失
        loss = (loss1 + loss2) / 2
        
        # 更新目标网络
        self._update_target_network()
        
        return loss
    
    def _byol_loss(self, online_pred, target_feat):
        """BYOL损失实现"""
        # 归一化特征
        online_pred = F.normalize(online_pred, dim=1)
        target_feat = F.normalize(target_feat, dim=1)
        
        # 负余弦相似度
        loss = 2 - 2 * (online_pred * target_feat).sum(dim=1).mean()
        return loss
    
    def get_feature(self, node_idx):
        """提取特征（用于下游任务）"""
        with torch.no_grad():
            # 使用在线编码器提取特征
            features = self.online_encoder(self.g, node_idx)
            return features 