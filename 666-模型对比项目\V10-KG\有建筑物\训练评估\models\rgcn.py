import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl.function as fn
import dgl

class RGCNLayer(nn.Module):
    """基于关系的图卷积层实现"""
    def __init__(self, in_dim, out_dim, num_rels, dropout=0.0):
        super(RGC<PERSON>ayer, self).__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.num_rels = num_rels
        self.dropout = nn.Dropout(dropout)
        self.activation = F.relu  # 添加激活函数属性
        
        # 为每种关系类型定义不同的权重矩阵
        self.weight_relations = nn.Parameter(torch.Tensor(num_rels, in_dim, out_dim))
        self.weight_self = nn.Parameter(torch.Tensor(in_dim, out_dim))
        self.bias = nn.Parameter(torch.Tensor(out_dim))
        
        # 初始化参数
        nn.init.xavier_uniform_(self.weight_relations)
        nn.init.xavier_uniform_(self.weight_self)
        nn.init.constant_(self.bias, 0.0)

    def safe_edge_subgraph(self, g, edge_mask):
        """创建一个边子图，并处理节点数量变化"""
        sub_g = g.edge_subgraph(edge_mask)
        # 创建从原始图到子图的节点映射
        orig_to_sub = {int(orig_id): i for i, orig_id in enumerate(sub_g.ndata[dgl.NID].tolist())}
        return sub_g, orig_to_sub

    def forward(self, g, feat, etypes):
        # 保存原始特征用于残差连接
        h = feat
        
        # 应用自环变换
        self_h = torch.matmul(h, self.weight_self)
        
        # 按关系类型聚合邻居信息
        g.ndata['h'] = h
        
        # 为每种关系单独处理，而不是一次处理所有关系
        out_feats = []
        for rel in torch.unique(etypes).long():
            # 创建这个关系的掩码
            rel_mask = (etypes == rel)
            
            # 获取这个关系对应的边
            sub_g = g.edge_subgraph(rel_mask)
            
            # 获取关系特定的变换
            transformed_h = torch.matmul(g.ndata['h'], self.weight_relations[rel])
            
            # 获取子图中节点到原图节点的映射
            sub_to_orig = sub_g.ndata[dgl.NID]
            
            # 将变换后的特征放到子图中（只保留实际在子图中的节点）
            sub_g.ndata['h_rel'] = transformed_h[sub_to_orig]
            sub_g.edata['norm'] = g.edata['norm'][rel_mask]
            
            # 在子图上进行消息传递
            sub_g.update_all(
                fn.u_mul_e('h_rel', 'norm', 'm'),
                fn.sum('m', 'h_agg_rel')
            )
            
            # 将结果从子图映射回完整图
            if 'h_agg_rel' in sub_g.ndata:
                # 创建一个全零张量用于存储聚合结果
                feat_dim = self_h.shape[1]
                h_agg = torch.zeros(g.num_nodes(), feat_dim).to(g.device)
                
                # 将子图结果复制到对应的位置
                h_agg[sub_to_orig] = sub_g.ndata['h_agg_rel']
                out_feats.append(h_agg)
        
        # 合并所有关系的结果
        if out_feats:
            out_h = torch.stack(out_feats).sum(dim=0)
        else:
            out_h = torch.zeros_like(self_h)
        
        # 残差连接和偏置
        out_h = out_h + self_h + self.bias
        
        # 激活函数
        if self.activation:
            out_h = self.activation(out_h)
        
        return out_h

class RGCN(nn.Module):
    """完整的RGCN模型"""
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(RGCN, self).__init__()
        self.g = kwargs['g']
        self.num_nodes = node_emb.shape[0]
        self.node_dim = node_emb.shape[1]
        self.num_rels = rel_emb.shape[0]
        
        # 初始节点和关系嵌入
        self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        self.rel_embeds = nn.Embedding.from_pretrained(rel_emb, freeze=True)
        
        # RGCN层
        self.layers = nn.ModuleList()
        layer_dims = [self.node_dim] + layer_sizes
        for i in range(len(layer_sizes)):
            self.layers.append(RGCNLayer(
                layer_dims[i], 
                layer_dims[i+1], 
                self.num_rels, 
                dropout=dropout_list[i]
            ))
        
        # 预测器（用于特征提取或下游任务）
        self.projector = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], layer_sizes[-1])
        )
    
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        h = self.node_embeds.weight
        etypes = self.g.edata['etype']
        
        # 通过RGCN层
        for layer in self.layers:
            h = layer(self.g, h, etypes)
        
        # 获取特定节点的嵌入
        h_node = h[node_idx]
        
        # 特征投影
        h_proj = self.projector(h_node)
        
        # 简单的自监督任务：预测自身
        return F.mse_loss(h_proj, h_node.detach())
    
    def get_feature(self, node_idx):
        """提取节点特征（用于下游任务）"""
        with torch.no_grad():
            h = self.node_embeds.weight
            etypes = self.g.edata['etype']
            
            # 通过RGCN层
            for layer in self.layers:
                h = layer(self.g, h, etypes)
            
            # 提取指定节点的特征
            if node_idx is not None:
                return h[node_idx]
            else:
                return h 