# 沈阳城市能耗数据集综合质量分析报告 (增强版)

## 📊 数据集概览

- **总区域数**: 757
- **训练集**: 605 个区域
- **验证集**: 75 个区域  
- **测试集**: 77 个区域

## 🕸️ 知识图谱深度分析

### 基础统计
- **节点数**: 200,558
- **边数**: 719,509
- **平均度**: 7.18

### 连通性分析
- **强连通性**: 否
- **强连通分量数**: 199817

### 实体类型分布
- **Other**: 694,454 (47.7%)
- **Region**: 537,345 (36.9%)
- **POI**: 221,820 (15.2%)
- **Facility**: 1,537 (0.1%)

## 🔍 特征异常检测结果

### 异常检测统计
- **有效样本数**: 757
- **孤立森林异常**: 76 个
- **聚类异常**: 15 个
- **多方法一致异常**: 9 个

### 高相关性特征对
未发现高相关性特征对

## 🔗 数据对齐完整性

- **能耗标签对齐率**: 757/757 (100.0%)
- **知识图谱对齐率**: 757/757 (100.0%)

## 📈 数据质量分析


- **有效能耗标签**: 757
- **能耗范围**: [0.000, 2.440]
- **能耗均值**: 0.995 ± 0.419
- **异常值比例**: 3.2%


## 🚨 发现的问题

1. 知识图谱连通性差，最大连通分量仅占0.4%
2. 发现15个区域连接度异常低

## 💡 推荐解决方案

1. 检查知识图谱构建过程，增加实体间的关联关系
2. 检查连接度异常低的区域，补充相关实体关系
3. 先运行模型训练生成预测结果

## 🎯 立即行动计划

1. **运行增强修复代码**: `python recommended_fixes_enhanced.py`
2. **处理异常区域**: 查看 `anomalous_regions_detail.json`
3. **优化知识图谱**: 改善图谱连通性和关系多样性
4. **应用特征工程**: 处理高相关性特征
5. **重新训练模型**: 使用修复后的配置

---

*增强报告生成时间: 2025-06-09 16:54:47*
