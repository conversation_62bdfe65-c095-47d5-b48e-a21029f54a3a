import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl
import dgl.nn as dglnn

class SemanticAttention(nn.Module):
    """语义级注意力层"""
    def __init__(self, in_size, hidden_size=128):
        super(SemanticAttention, self).__init__()
        self.project = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.Tanh(),
            nn.Linear(hidden_size, 1, bias=False)
        )

    def forward(self, z):
        # z: [num_relations, num_nodes, feature_dim]
        w = self.project(z)  # [num_relations, num_nodes, 1]
        beta = torch.softmax(w, dim=0)  # [num_relations, num_nodes, 1]
        return (beta * z).sum(0)  # [num_nodes, feature_dim]

class HANLayer(nn.Module):
    """异构图注意力网络层（修复版）"""
    def __init__(self, in_dim, out_dim, num_relations, num_heads, dropout=0.0):
        super(HANLayer, self).__init__()
        self.num_relations = num_relations
        self.num_heads = num_heads
        self.out_dim = out_dim
        
        # 不同关系的GAT层
        self.rel_gats = nn.ModuleList()
        for _ in range(num_relations):
            self.rel_gats.append(dglnn.GATConv(
                in_feats=in_dim,
                out_feats=out_dim,
                num_heads=num_heads,
                feat_drop=dropout,
                attn_drop=dropout,
                activation=F.elu,
                allow_zero_in_degree=True
            ))
        
        # 语义注意力层 - 修正输入维度
        self.semantic_attention = SemanticAttention(out_dim * num_heads)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, g, h, rel_edges):
        # 关系特定的GAT
        rel_features = []
        
        for rel_id in range(self.num_relations):
            # 创建此关系对应的边子图
            if rel_id < len(g.edata['etype'].unique()):
                rel_mask = (g.edata['etype'] == rel_id)
                if rel_mask.any():
                    sub_g = g.edge_subgraph(rel_mask)
                    sub_to_orig = sub_g.ndata[dgl.NID]
                    
                    # 应用GAT到此关系
                    rel_feat = self.rel_gats[rel_id](sub_g, h[sub_to_orig])
                    
                    # 正确处理多头输出
                    if rel_feat.dim() == 3:  # [nodes, heads, features]
                        rel_feat = rel_feat.reshape(rel_feat.shape[0], -1)
                    
                    # 将关系特定的节点特征映射回全图
                    orig_feat = torch.zeros(h.shape[0], rel_feat.shape[1], device=h.device)
                    orig_feat[sub_to_orig] = rel_feat
                else:
                    # 如果没有这种关系的边，创建零特征
                    orig_feat = torch.zeros(h.shape[0], self.out_dim * self.num_heads, device=h.device)
            else:
                # 超出关系范围，创建零特征
                orig_feat = torch.zeros(h.shape[0], self.out_dim * self.num_heads, device=h.device)
            
            rel_features.append(orig_feat)
        
        # 通过语义注意力整合不同关系的特征
        if rel_features:
            stacked_features = torch.stack(rel_features, dim=0)  # [num_rels, num_nodes, feat_dim]
            h_out = self.semantic_attention(stacked_features)  # [num_nodes, feat_dim]
        else:
            h_out = torch.zeros(h.shape[0], self.out_dim * self.num_heads, device=h.device)
        
        return self.dropout(h_out)

class HAN(nn.Module):
    """异构图注意力网络模型（修复版）"""
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(HAN, self).__init__()
        self.g = kwargs['g']
        self.num_nodes = node_emb.shape[0]
        self.node_dim = node_emb.shape[1]
        self.num_rels = len(torch.unique(self.g.edata['etype']))  # 实际的关系类型数
        
        # 初始节点嵌入
        self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        
        # HAN层
        self.layers = nn.ModuleList()
        
        # 修正层配置
        num_heads = [4, 1]  # 减少第一层的头数以避免维度爆炸
        
        # 第一层
        self.layers.append(HANLayer(
            self.node_dim,
            layer_sizes[0] // num_heads[0],  # 确保输出维度正确
            num_relations=self.num_rels,
            num_heads=num_heads[0],
            dropout=dropout_list[0]
        ))
        
        # 第二层
        if len(layer_sizes) > 1:
            self.layers.append(HANLayer(
                layer_sizes[0],  # 第一层的输出维度
                layer_sizes[1],
                num_relations=self.num_rels,
                num_heads=num_heads[1],
                dropout=dropout_list[1]
            ))
        
        # 预测器
        final_dim = layer_sizes[-1] if num_heads[-1] == 1 else layer_sizes[-1] * num_heads[-1]
        self.projector = nn.Sequential(
            nn.Linear(final_dim, layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], layer_sizes[-1])
        )
        
        # 创建每种关系的边索引
        self.rel_edges = {}
        for rel_id in range(self.num_rels):
            rel_mask = (self.g.edata['etype'] == rel_id)
            self.rel_edges[rel_id] = rel_mask
    
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        h = self.node_embeds.weight
        
        # 通过HAN层
        for layer in self.layers:
            h = layer(self.g, h, self.rel_edges)
        
        # 获取特定节点的嵌入
        h_node = h[node_idx]
        
        # 特征投影
        h_proj = self.projector(h_node)
        
        # 简单的自监督任务：预测自身
        return F.mse_loss(h_proj, h_node.detach())
    
    def get_feature(self, node_idx):
        """提取节点特征（用于下游任务）"""
        with torch.no_grad():
            h = self.node_embeds.weight
            
            # 通过HAN层
            for layer in self.layers:
                h = layer(self.g, h, self.rel_edges)
            
            # 提取指定节点的特征
            if node_idx is not None:
                return h[node_idx]
            else:
                return h