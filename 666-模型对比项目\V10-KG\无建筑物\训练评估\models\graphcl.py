import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from models.graph_encoder import GraphEncoder

def get_resnet(name, pretrained=False):
    resnets = {
        "resnet18": models.resnet18(pretrained=pretrained),
        "resnet50": models.resnet50(pretrained=pretrained),
    }
    if name not in resnets.keys():
        raise KeyError(f"{name} is not a valid ResNet version")
    return resnets[name]

class Identity(nn.Module):
    def __init__(self):
        super(Identity, self).__init__()
        
    def forward(self, x):
        return x

class GraphCL(nn.Module):
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(GraphCL, self).__init__()
        self.g = kwargs['g']
        
        # 图编码器（使用CompGCN）
        self.graph_encoder = GraphEncoder(node_emb, rel_emb, layer_sizes, dropout_list)
        
        # 图像编码器（ResNet）
        self.image_encoder = get_resnet('resnet18', pretrained=True)
        self.image_encoder.fc = Identity()
        
        # 图特征投影
        self.graph_projector = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], 64)
        )
        
        # 图像特征投影
        self.image_projector = nn.Sequential(
            nn.Linear(512, 512),
            nn.ReLU(),
            nn.Linear(512, 64)
        )
    
    def forward(self, images, node_idx):
        """前向传播（用于训练）"""
        # 提取图特征
        graph_features = self.graph_encoder(self.g, node_idx)
        graph_features = self.graph_projector(graph_features)
        
        # 提取图像特征
        image_features = self.image_encoder(images)
        image_features = self.image_projector(image_features)
        
        # 跨模态对比损失
        loss = self._cross_modal_contrastive_loss(graph_features, image_features)
        return loss
    
    def _cross_modal_contrastive_loss(self, graph_features, image_features):
        """跨模态对比损失实现"""
        # 归一化特征
        graph_features = F.normalize(graph_features, dim=1)
        image_features = F.normalize(image_features, dim=1)
        
        # 计算相似度矩阵
        sim_matrix = torch.matmul(graph_features, image_features.t())
        
        # 对角线上是正样本（相同索引的图和图像特征应该接近）
        labels = torch.arange(graph_features.shape[0]).to(graph_features.device)
        
        # 计算图->图像方向的对比损失
        loss_g2i = F.cross_entropy(sim_matrix / 0.1, labels)
        
        # 计算图像->图方向的对比损失
        loss_i2g = F.cross_entropy(sim_matrix.t() / 0.1, labels)
        
        # 总损失
        loss = (loss_g2i + loss_i2g) / 2
        return loss
    
    def get_feature(self, images=None, node_idx=None):
        """提取特征（用于下游任务）"""
        with torch.no_grad():
            if images is not None and node_idx is not None:
                # 提取图特征
                graph_features = self.graph_encoder(self.g, node_idx)
                
                # 提取图像特征
                image_features = self.image_encoder(images)
                
                # 连接特征
                combined_features = torch.cat([graph_features, image_features], dim=1)
                return combined_features
            elif images is not None:
                # 仅提取图像特征
                return self.image_encoder(images)
            elif node_idx is not None:
                # 仅提取图特征
                return self.graph_encoder(self.g, node_idx)
            else:
                raise ValueError("必须提供images或node_idx之一") 