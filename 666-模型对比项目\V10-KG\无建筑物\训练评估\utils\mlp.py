import torch
import torch.nn as nn
import numpy as np
import json
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import os

class EnergyMLP(nn.Module):
    """能耗预测MLP模型"""
    def __init__(self, input_dim, hidden_dims=[256, 128, 64], dropout=0.3):
        super(EnergyMLP, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.BatchNorm1d(hidden_dim)
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x).squeeze()

def calculate_mape(y_true, y_pred):
    """计算MAPE，处理零值和极小值情况"""
    # 避免除零错误和极小值导致的爆炸
    epsilon = 1e-7
    mask = np.abs(y_true) > epsilon
    
    if not mask.any():
        return 0.0
    
    # 计算相对误差，但限制最大值避免爆炸
    relative_errors = np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])
    # 限制单个误差的最大值为10（1000%）
    relative_errors = np.minimum(relative_errors, 10.0)
    
    mape = np.mean(relative_errors) * 100
    return mape

def train_energy_predictor(args, kg_features, region_info_path):
    """训练能耗预测器"""
    print("\n=== 开始能耗预测流程 ===")
    
    # 1. 加载区域能耗数据
    print(f"加载区域能耗数据: {region_info_path}")
    with open(region_info_path, 'r', encoding='utf-8') as f:
        region_data = json.load(f)
    
    print(f"加载了 {len(region_data)} 个区域的能耗数据")
    
    # 2. 准备数据
    print("准备训练数据...")
    
    # 提取特征和标签
    features = []
    labels = []
    valid_regions = []
    
    # 确保KG特征与区域对应
    for i, (region_id, data) in enumerate(region_data.items()):
        if i < len(kg_features) and isinstance(data, dict) and 'energy' in data:
            try:
                energy = float(data['energy'])
                
                # 提取额外特征
                extra_features = []
                for key in ['population', 'area', 'building_count', 'poi_count', 'road_density']:
                    if key in data:
                        try:
                            extra_features.append(float(data[key]))
                        except:
                            extra_features.append(0.0)
                    else:
                        extra_features.append(0.0)
                
                # 合并KG特征和额外特征
                combined_features = np.concatenate([kg_features[i], extra_features])
                
                features.append(combined_features)
                labels.append(energy)
                valid_regions.append(region_id)
                
            except Exception as e:
                print(f"跳过区域 {region_id}: {e}")
    
    features = np.array(features)
    labels = np.array(labels)
    
    print(f"合并特征: KG特征({kg_features.shape[1]}维) + 额外特征({len(extra_features)}维)")
    
    # 3. 数据标准化
    scaler_x = StandardScaler()
    scaler_y = StandardScaler()
    
    features_scaled = scaler_x.fit_transform(features)
    labels_scaled = scaler_y.fit_transform(labels.reshape(-1, 1)).squeeze()
    
    # 4. 划分数据集
    X_train, X_test, y_train, y_test, idx_train, idx_test = train_test_split(
        features_scaled, labels_scaled, range(len(labels)), 
        test_size=0.2, random_state=42
    )
    
    print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 5. 转换为PyTorch张量
    X_train_torch = torch.FloatTensor(X_train)
    y_train_torch = torch.FloatTensor(y_train)
    X_test_torch = torch.FloatTensor(X_test)
    y_test_torch = torch.FloatTensor(y_test)
    
    # 6. 初始化模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = EnergyMLP(input_dim=features.shape[1]).to(device)
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    # 7. 训练模型
    print("开始训练MLP模型...")
    
    best_val_loss = float('inf')
    patience = 20
    patience_counter = 0
    
    X_train_torch = X_train_torch.to(device)
    y_train_torch = y_train_torch.to(device)
    X_test_torch = X_test_torch.to(device)
    y_test_torch = y_test_torch.to(device)
    
    for epoch in range(200):
        # 训练
        model.train()
        optimizer.zero_grad()
        
        train_pred = model(X_train_torch)
        train_loss = criterion(train_pred, y_train_torch)
        
        train_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        # 验证
        model.eval()
        with torch.no_grad():
            val_pred = model(X_test_torch)
            val_loss = criterion(val_pred, y_test_torch)
        
        scheduler.step(val_loss)
        
        if epoch == 0 or (epoch + 1) % 20 == 0:
            print(f"Epoch [{epoch+1}/200], Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # 早停
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            best_model_state = model.state_dict()
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"早停触发，在第{epoch+1}轮停止训练")
                break
    
    # 8. 加载最佳模型并评估
    model.load_state_dict(best_model_state)
    model.eval()
    
    print("评估模型性能...")
    with torch.no_grad():
        # 测试集预测
        test_pred_scaled = model(X_test_torch).cpu().numpy()
        test_pred = scaler_y.inverse_transform(test_pred_scaled.reshape(-1, 1)).squeeze()
        test_true = scaler_y.inverse_transform(y_test.reshape(-1, 1)).squeeze()
        
        # 全部数据预测（用于可视化）
        all_features_torch = torch.FloatTensor(features_scaled).to(device)
        all_pred_scaled = model(all_features_torch).cpu().numpy()
        all_pred = scaler_y.inverse_transform(all_pred_scaled.reshape(-1, 1)).squeeze()
    
    # 9. 计算评估指标
    r2 = r2_score(test_true, test_pred)
    rmse = np.sqrt(mean_squared_error(test_true, test_pred))
    mae = mean_absolute_error(test_true, test_pred)
    mape = calculate_mape(test_true, test_pred)
    
    # 10. 保存结果
    results_dir = getattr(args, 'model_path', './saved_models/')
    os.makedirs(results_dir, exist_ok=True)
    
    # 保存详细结果
    detailed_results = {
        'test_regions': [valid_regions[i] for i in idx_test],
        'predictions': test_pred.tolist(),
        'true_values': test_true.tolist(),
        'metrics': {
            'r2': float(r2),
            'rmse': float(rmse),
            'mae': float(mae),
            'mape': float(mape)
        },
        'feature_importance': {
            'kg_feature_dim': kg_features.shape[1],
            'extra_features': len(extra_features),
            'total_features': features.shape[1]
        }
    }
    
    results_file = os.path.join(results_dir, f"{args.dataset}_{args.model}_energy_results.json")
    with open(results_file, 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"结果已保存到 {results_file}")
    
    # 返回预测结果
    return test_pred, r2, rmse, mae, mape, test_true