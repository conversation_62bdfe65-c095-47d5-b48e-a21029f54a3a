#!/usr/bin/env python3
"""
统一数据质量检查工具 - 增强版

综合功能:
1. 数据对齐完整性检查 
2. 数据质量和分布分析  
3. 知识图谱深度分析 (增强)
4. 特征异常值检测 (新增)
5. 模型预测结果分析
6. 针对性解决方案推荐
7. 自动生成修复后的配置和代码
"""

import os
import json
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
from PIL import Image
import warnings
import networkx as nx
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
from sklearn.cluster import DBSCAN
warnings.filterwarnings('ignore')

# 导入配置
from config import get_config

class UnifiedDataQualityChecker:
    """统一数据质量检查器 - 增强版"""
    
    def __init__(self, dataset_name="shenyang", output_dir="./quality_analysis/"):
        """
        初始化检查器
        
        Args:
            dataset_name: 数据集名称
            output_dir: 输出目录
        """
        self.dataset_name = dataset_name
        self.config = get_config(dataset_name)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.results = {}
        self.issues = []
        self.recommendations = []
        
        # 知识图谱相关
        self.kg_graph = None
        self.kg_metrics = {}
        
        # 特征异常检测器
        self.feature_anomaly_detectors = {}
        
    def run_comprehensive_check(self):
        """运行综合检查"""
        print("🔍 启动统一数据质量综合检查...")
        print("=" * 80)
        
        # 1. 基础数据加载
        self._load_core_data()
        
        # 2. 数据集格式展示
        self._display_dataset_format()
        
        # 3. 数据对齐完整性检查
        self._check_data_alignment()
        
        # 4. 知识图谱深度分析 (增强)
        self._analyze_knowledge_graph_enhanced()
        
        # 5. 特征异常值检测 (新增)
        self._detect_feature_anomalies()
        
        # 6. 数据质量分析
        self._analyze_data_quality()
        
        # 7. 数据分布分析
        self._analyze_data_distribution()
        
        # 8. 图像数据检查
        self._check_image_availability()
        
        # 9. 模型预测结果分析
        self._analyze_model_predictions()
        
        # 10. 问题诊断和解决方案
        self._diagnose_and_recommend()
        
        # 11. 生成可视化报告 (增强)
        self._generate_enhanced_visualizations()
        
        # 12. 生成修复代码
        self._generate_fix_code()
        
        # 13. 保存综合报告
        self._save_comprehensive_report()
        
        return self.results
    
    def _load_core_data(self):
        """加载核心数据"""
        print("\n📋 1. 加载核心数据")
        print("-" * 50)
        
        # 加载区域信息 (修复路径配置)
        with open(self.config['region_info_path'], 'r', encoding='utf-8') as f:
            self.region_info = json.load(f)
        
        # 加载知识图谱并进行深度分析
        self.kg_entities = set()
        self.kg_relations = set()
        self.kg_triples = []
        self.entity_types = defaultdict(int)
        self.relation_types = defaultdict(int)
        self.kg_graph = nx.DiGraph()  # 使用NetworkX构建图
        
        print("🔍 加载和分析知识图谱...")
        with open(self.config['kg_path'], 'r', encoding='utf-8') as f:
            for line_no, line in enumerate(f):
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    head, relation, tail = parts[0], parts[1], parts[2]
                    self.kg_entities.add(head)
                    self.kg_entities.add(tail)
                    self.kg_relations.add(relation)
                    self.kg_triples.append((head, relation, tail))
                    
                    # 构建图结构
                    self.kg_graph.add_edge(head, tail, relation=relation)
                    
                    # 分析实体类型
                    head_type = self._get_entity_type(head)
                    tail_type = self._get_entity_type(tail)
                    self.entity_types[head_type] += 1
                    self.entity_types[tail_type] += 1
                    
                    # 分析关系类型
                    self.relation_types[relation] += 1
        
        # 加载数据集划分 (修复路径配置)
        try:
            self.train_df = pd.read_csv(self.config['train_path'])
            self.val_df = pd.read_csv(self.config['val_path'])
            self.test_df = pd.read_csv(self.config['test_path'])
        except Exception as e:
            print(f"⚠️ 数据集划分文件加载失败: {e}")
            # 创建默认的数据集划分
            all_block_ids = list(self.region_info.keys())
            n_total = len(all_block_ids)
            n_train = int(0.7 * n_total)
            n_val = int(0.15 * n_total)
            
            self.train_df = pd.DataFrame({'BlockID': all_block_ids[:n_train]})
            self.val_df = pd.DataFrame({'BlockID': all_block_ids[n_train:n_train+n_val]})
            self.test_df = pd.DataFrame({'BlockID': all_block_ids[n_train+n_val:]})
            
            print("🔄 已创建默认数据集划分")
        
        self.all_block_ids = set(
            list(self.train_df['BlockID']) + 
            list(self.val_df['BlockID']) + 
            list(self.test_df['BlockID'])
        )
        
        print(f"✅ 核心数据加载完成")
        print(f"   - 区域信息: {len(self.region_info)} 个区域")
        print(f"   - 知识图谱: {len(self.kg_entities)} 个实体, {len(self.kg_relations)} 种关系, {len(self.kg_triples)} 个三元组")
        print(f"   - 训练集: {len(self.train_df)} 个区域")
        print(f"   - 验证集: {len(self.val_df)} 个区域")
        print(f"   - 测试集: {len(self.test_df)} 个区域")
        print(f"   - 总区域: {len(self.all_block_ids)} 个区域")
        
        self.results['data_loading'] = {
            'region_info_count': len(self.region_info),
            'kg_entities_count': len(self.kg_entities),
            'kg_relations_count': len(self.kg_relations),
            'kg_triples_count': len(self.kg_triples),
            'train_count': len(self.train_df),
            'val_count': len(self.val_df), 
            'test_count': len(self.test_df),
            'total_unique_blocks': len(self.all_block_ids)
        }
    
    def _analyze_knowledge_graph_enhanced(self):
        """增强的知识图谱分析"""
        print("\n🕸️ 4. 知识图谱深度分析 (增强版)")
        print("-" * 50)
        
        # 基础图谱统计
        print("📈 图谱基础统计:")
        print(f"   - 节点数: {self.kg_graph.number_of_nodes():,}")
        print(f"   - 边数: {self.kg_graph.number_of_edges():,}")
        print(f"   - 平均度: {np.mean(list(dict(self.kg_graph.degree()).values())):.2f}")
        
        # 1. 图连通性分析
        print("\n🔗 图连通性分析:")
        if nx.is_strongly_connected(self.kg_graph):
            print("   ✅ 图是强连通的")
        else:
            strongly_connected_components = list(nx.strongly_connected_components(self.kg_graph))
            largest_scc_size = max(len(scc) for scc in strongly_connected_components)
            print(f"   ⚠️ 图不是强连通的")
            print(f"   - 强连通分量数: {len(strongly_connected_components)}")
            print(f"   - 最大强连通分量: {largest_scc_size} 个节点 ({largest_scc_size/self.kg_graph.number_of_nodes():.1%})")
            
            if largest_scc_size < self.kg_graph.number_of_nodes() * 0.8:
                self.issues.append(f"知识图谱连通性差，最大连通分量仅占{largest_scc_size/self.kg_graph.number_of_nodes():.1%}")
                self.recommendations.append("检查知识图谱构建过程，增加实体间的关联关系")
        
        # 2. 中心性分析
        print("\n🎯 中心性分析:")
        if self.kg_graph.number_of_nodes() > 0:
            # 度中心性
            degree_centrality = nx.degree_centrality(self.kg_graph)
            top_degree_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # 介数中心性 (对大图进行采样)
            if self.kg_graph.number_of_nodes() > 1000:
                sample_nodes = list(self.kg_graph.nodes())[:1000]
                subgraph = self.kg_graph.subgraph(sample_nodes)
                betweenness_centrality = nx.betweenness_centrality(subgraph)
            else:
                betweenness_centrality = nx.betweenness_centrality(self.kg_graph)
            
            top_betweenness_nodes = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            
            print("   🔝 度中心性最高的节点:")
            for node, centrality in top_degree_nodes:
                node_type = self._get_entity_type(node)
                print(f"     - {node} ({node_type}): {centrality:.3f}")
            
            print("   🌉 介数中心性最高的节点:")
            for node, centrality in top_betweenness_nodes:
                node_type = self._get_entity_type(node)
                print(f"     - {node} ({node_type}): {centrality:.3f}")
        
        # 3. 实体类型分布分析
        print("\n🏷️ 实体类型分布:")
        total_entities = sum(self.entity_types.values())
        for entity_type, count in sorted(self.entity_types.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_entities * 100
            print(f"   - {entity_type}: {count:,} ({percentage:.1f}%)")
        
        # 4. 关系类型分析
        print("\n🔗 关系类型分析:")
        total_relations = sum(self.relation_types.values())
        sorted_relations = sorted(self.relation_types.items(), key=lambda x: x[1], reverse=True)
        
        print("   🔝 最频繁的关系类型:")
        for relation, count in sorted_relations[:10]:
            percentage = count / total_relations * 100
            print(f"     - {relation}: {count:,} ({percentage:.1f}%)")
        
        # 检查关系分布是否均衡
        relation_counts = list(self.relation_types.values())
        if len(relation_counts) > 1:
            relation_gini = self._calculate_gini_coefficient(relation_counts)
            print(f"   📊 关系分布基尼系数: {relation_gini:.3f}")
            
            if relation_gini > 0.8:
                self.issues.append(f"关系类型分布极不均衡 (基尼系数: {relation_gini:.3f})")
                self.recommendations.append("检查关系抽取过程，确保关系类型多样性")
        
        # 5. 区域实体连接度分析
        print("\n🗺️ 区域实体连接度分析:")
        region_entities = [node for node in self.kg_graph.nodes() if node.startswith('Region_')]
        region_connections = {}
        
        for region in region_entities:
            in_degree = self.kg_graph.in_degree(region)
            out_degree = self.kg_graph.out_degree(region)
            total_degree = in_degree + out_degree
            region_connections[region] = total_degree
        
        if region_connections:
            avg_connections = np.mean(list(region_connections.values()))
            std_connections = np.std(list(region_connections.values()))
            
            print(f"   - 平均连接度: {avg_connections:.2f} ± {std_connections:.2f}")
            print(f"   - 连接度范围: [{min(region_connections.values())}, {max(region_connections.values())}]")
            
            # 找出连接度异常的区域
            low_threshold = avg_connections - 2 * std_connections
            high_threshold = avg_connections + 2 * std_connections
            
            low_connected_regions = [r for r, c in region_connections.items() if c < max(0, low_threshold)]
            high_connected_regions = [r for r, c in region_connections.items() if c > high_threshold]
            
            if low_connected_regions:
                print(f"   ⚠️ 连接度异常低的区域: {len(low_connected_regions)} 个")
                self.issues.append(f"发现{len(low_connected_regions)}个区域连接度异常低")
                self.recommendations.append("检查连接度异常低的区域，补充相关实体关系")
            
            if high_connected_regions:
                print(f"   📈 连接度异常高的区域: {len(high_connected_regions)} 个")
        
        # 6. 路径长度分析
        print("\n🛣️ 路径长度分析:")
        if nx.is_connected(self.kg_graph.to_undirected()):
            avg_path_length = nx.average_shortest_path_length(self.kg_graph.to_undirected())
            diameter = nx.diameter(self.kg_graph.to_undirected())
            print(f"   - 平均最短路径长度: {avg_path_length:.2f}")
            print(f"   - 图直径: {diameter}")
            
            if avg_path_length > 6:
                self.issues.append(f"图的平均路径长度过长: {avg_path_length:.2f}")
                self.recommendations.append("考虑增加更多直接连接以缩短路径长度")
        else:
            print("   ⚠️ 图不连通，无法计算平均路径长度")
        
        # 保存知识图谱分析结果
        self.kg_metrics = {
            'basic_stats': {
                'nodes': self.kg_graph.number_of_nodes(),
                'edges': self.kg_graph.number_of_edges(),
                'avg_degree': np.mean(list(dict(self.kg_graph.degree()).values()))
            },
            'connectivity': {
                'is_strongly_connected': nx.is_strongly_connected(self.kg_graph),
                'num_scc': len(list(nx.strongly_connected_components(self.kg_graph)))
            },
            'entity_types': dict(self.entity_types),
            'relation_types': dict(self.relation_types),
            'region_connections': region_connections if 'region_connections' in locals() else {}
        }
        
        self.results['kg_analysis_enhanced'] = self.kg_metrics
    
    def _detect_feature_anomalies(self):
        """特征异常值检测"""
        print("\n🔍 5. 特征异常值检测 (新增)")
        print("-" * 50)
        
        # 提取数值特征
        numerical_features = []
        feature_names = []
        
        # 收集所有数值特征
        for region_id, region_data in self.region_info.items():
            if isinstance(region_data, dict):
                for key, value in region_data.items():
                    if isinstance(value, (int, float)) and key not in ['BlockID']:
                        if key not in feature_names:
                            feature_names.append(key)
        
        print(f"🔢 发现数值特征: {len(feature_names)} 个")
        print(f"   特征列表: {feature_names}")
        
        # 构建特征矩阵
        feature_matrix = []
        valid_region_ids = []
        
        for region_id, region_data in self.region_info.items():
            if isinstance(region_data, dict):
                feature_row = []
                valid_row = True
                
                for feature_name in feature_names:
                    if feature_name in region_data:
                        value = region_data[feature_name]
                        if isinstance(value, (int, float)) and not np.isnan(value):
                            feature_row.append(float(value))
                        else:
                            valid_row = False
                            break
                    else:
                        valid_row = False
                        break
                
                if valid_row and len(feature_row) == len(feature_names):
                    feature_matrix.append(feature_row)
                    valid_region_ids.append(region_id)
        
        if len(feature_matrix) == 0:
            print("⚠️ 没有足够的有效数值特征数据进行异常检测")
            return
        
        feature_matrix = np.array(feature_matrix)
        print(f"📊 有效样本数: {len(feature_matrix)}")
        
        # 1. 统计异常检测 (Z-score)
        print("\n📈 1. 统计异常检测 (Z-score):")
        z_scores = np.abs(stats.zscore(feature_matrix, axis=0))
        z_threshold = 3.0
        
        statistical_anomalies = {}
        for i, feature_name in enumerate(feature_names):
            feature_z_scores = z_scores[:, i]
            anomaly_mask = feature_z_scores > z_threshold
            anomaly_count = np.sum(anomaly_mask)
            anomaly_percentage = anomaly_count / len(feature_matrix) * 100
            
            statistical_anomalies[feature_name] = {
                'count': int(anomaly_count),
                'percentage': float(anomaly_percentage),
                'max_z_score': float(np.max(feature_z_scores)),
                'anomaly_indices': np.where(anomaly_mask)[0].tolist()
            }
            
            print(f"   - {feature_name}: {anomaly_count} 个异常值 ({anomaly_percentage:.1f}%)")
            
            if anomaly_percentage > 10:
                self.issues.append(f"特征 {feature_name} 异常值过多: {anomaly_percentage:.1f}%")
                self.recommendations.append(f"检查特征 {feature_name} 的数据收集过程")
        
        # 2. 孤立森林异常检测
        print("\n🌲 2. 孤立森林异常检测:")
        isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        isolation_anomalies = isolation_forest.fit_predict(feature_matrix)
        isolation_anomaly_count = np.sum(isolation_anomalies == -1)
        isolation_anomaly_percentage = isolation_anomaly_count / len(feature_matrix) * 100
        
        print(f"   - 检测到异常样本: {isolation_anomaly_count} 个 ({isolation_anomaly_percentage:.1f}%)")
        
        # 3. 基于聚类的异常检测 (DBSCAN)
        print("\n🎯 3. 基于聚类的异常检测 (DBSCAN):")
        # 标准化特征
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(feature_matrix)
        
        # DBSCAN聚类
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        cluster_labels = dbscan.fit_predict(scaled_features)
        
        # 噪声点被标记为-1
        noise_mask = cluster_labels == -1
        noise_count = np.sum(noise_mask)
        noise_percentage = noise_count / len(feature_matrix) * 100
        
        n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
        
        print(f"   - 聚类数量: {n_clusters}")
        print(f"   - 噪声点(异常): {noise_count} 个 ({noise_percentage:.1f}%)")
        
        # 4. 多元异常检测汇总
        print("\n📋 4. 异常检测汇总:")
        
        # 找出在多种方法中都被标记为异常的样本
        isolation_anomaly_indices = set(np.where(isolation_anomalies == -1)[0])
        dbscan_anomaly_indices = set(np.where(noise_mask)[0])
        
        # 统计特征的统计异常
        statistical_anomaly_indices = set()
        for feature_name, anomaly_info in statistical_anomalies.items():
            statistical_anomaly_indices.update(anomaly_info['anomaly_indices'])
        
        # 多方法一致异常
        consistent_anomalies = isolation_anomaly_indices & dbscan_anomaly_indices & statistical_anomaly_indices
        
        print(f"   - 孤立森林异常: {len(isolation_anomaly_indices)} 个")
        print(f"   - 聚类异常: {len(dbscan_anomaly_indices)} 个")
        print(f"   - 统计异常: {len(statistical_anomaly_indices)} 个")
        print(f"   - 多方法一致异常: {len(consistent_anomalies)} 个")
        
        if len(consistent_anomalies) > 0:
            print(f"   🚨 需要重点关注的异常区域:")
            for idx in list(consistent_anomalies)[:10]:  # 显示前10个
                region_id = valid_region_ids[idx]
                print(f"     - 区域 {region_id}")
        
        # 5. 特征相关性异常检测
        print("\n🔗 5. 特征相关性分析:")
        correlation_matrix = np.corrcoef(feature_matrix.T)
        
        # 找出高度相关的特征对
        high_corr_pairs = []
        for i in range(len(feature_names)):
            for j in range(i+1, len(feature_names)):
                corr = correlation_matrix[i, j]
                if abs(corr) > 0.9:
                    high_corr_pairs.append((feature_names[i], feature_names[j], corr))
        
        if high_corr_pairs:
            print(f"   ⚠️ 发现高度相关的特征对:")
            for feat1, feat2, corr in high_corr_pairs:
                print(f"     - {feat1} ↔ {feat2}: {corr:.3f}")
            
            self.issues.append(f"发现{len(high_corr_pairs)}对高度相关的特征")
            self.recommendations.append("考虑特征选择或降维处理高度相关的特征")
        
        # 保存异常检测结果
        anomaly_results = {
            'statistical_anomalies': statistical_anomalies,
            'isolation_forest': {
                'anomaly_count': int(isolation_anomaly_count),
                'anomaly_percentage': float(isolation_anomaly_percentage),
                'anomaly_indices': isolation_anomaly_indices
            },
            'dbscan_clustering': {
                'n_clusters': int(n_clusters),
                'noise_count': int(noise_count),
                'noise_percentage': float(noise_percentage),
                'anomaly_indices': dbscan_anomaly_indices
            },
            'consistent_anomalies': {
                'count': len(consistent_anomalies),
                'indices': list(consistent_anomalies),
                'region_ids': [valid_region_ids[idx] for idx in consistent_anomalies]
            },
            'high_correlation_pairs': high_corr_pairs,
            'feature_matrix_shape': feature_matrix.shape,
            'valid_region_count': len(valid_region_ids)
        }
        
        self.results['feature_anomaly_detection'] = anomaly_results
        
        # 保存异常区域的详细信息
        if len(consistent_anomalies) > 0:
            anomaly_details = []
            for idx in consistent_anomalies:
                region_id = valid_region_ids[idx]
                region_data = self.region_info.get(region_id, {})
                
                anomaly_detail = {
                    'region_id': region_id,
                    'features': {name: region_data.get(name, 'N/A') for name in feature_names},
                    'z_scores': {name: float(z_scores[idx, i]) for i, name in enumerate(feature_names)}
                }
                anomaly_details.append(anomaly_detail)
            
            # 保存异常区域详情到文件
            anomaly_file = self.output_dir / 'anomalous_regions_detail.json'
            with open(anomaly_file, 'w', encoding='utf-8') as f:
                json.dump(anomaly_details, f, indent=2, ensure_ascii=False)
            
            print(f"   💾 异常区域详情已保存到: {anomaly_file}")
    
    def _calculate_gini_coefficient(self, values):
        """计算基尼系数"""
        values = np.array(values)
        values = np.sort(values)
        n = len(values)
        index = np.arange(1, n + 1)
        return (2 * np.sum(index * values)) / (n * np.sum(values)) - (n + 1) / n
    
    def _get_entity_type(self, entity):
        """识别实体类型"""
        entity_lower = entity.lower()
        if entity.startswith('Region_'):
            return 'Region'
        elif 'poi_' in entity_lower:
            return 'POI'
        elif any(word in entity_lower for word in ['road', 'street', 'avenue', 'way']):
            return 'Road'
        elif any(word in entity_lower for word in ['station', 'stop', 'terminal']):
            return 'Transport'
        elif any(word in entity_lower for word in ['park', 'garden', 'plaza']):
            return 'Landmark'
        elif any(word in entity_lower for word in ['school', 'hospital', 'bank', 'restaurant', 'shop']):
            return 'Facility'
        else:
            return 'Other'
    
    def _display_dataset_format(self):
        """展示数据集格式和结构"""
        print("\n📋 2. 数据集格式展示")
        print("-" * 50)
        
        # 使用config.py中的实际配置路径
        print("📁 数据文件结构:")
        print(f"   - 知识图谱: {self.config['kg_path']}")
        print(f"   - 区域信息: {self.config['region_info_path']}")
        print(f"   - 训练集: {self.config['train_path']}")
        print(f"   - 验证集: {self.config['val_path']}")
        print(f"   - 测试集: {self.config['test_path']}")
        print(f"   - 预训练嵌入: {self.config['pretrain_path']}")
        
        # 展示区域信息示例
        if self.region_info:
            sample_key = list(self.region_info.keys())[0]
            sample_data = self.region_info[sample_key]
            print(f"\n📊 区域信息示例 (区域 {sample_key}):")
            if isinstance(sample_data, dict):
                for key, value in sample_data.items():
                    print(f"   - {key}: {type(value).__name__} = {value}")
    
    def _check_data_alignment(self):
        """检查数据对齐完整性"""
        print("\n🔗 3. 数据对齐完整性检查")
        print("-" * 50)
        
        alignment_stats = {
            'kg_aligned': 0,
            'energy_aligned': 0,
            'fully_aligned': 0,
            'missing_details': {}
        }
        
        # 检查每个区域的对齐情况
        for block_id in self.all_block_ids:
            region_kg_key = f"Region_{block_id}"
            region_energy_key = str(block_id)
            
            alignment = {
                'kg_entity': False,
                'energy_label': False
            }
            
            # 知识图谱对齐检查
            if region_kg_key in self.kg_entities:
                alignment['kg_entity'] = True
                alignment_stats['kg_aligned'] += 1
            
            # 能耗标签对齐检查
            if region_energy_key in self.region_info:
                region_data = self.region_info[region_energy_key]
                if isinstance(region_data, dict) and 'energy' in region_data:
                    try:
                        energy_val = float(region_data['energy'])
                        alignment['energy_label'] = True
                        alignment_stats['energy_aligned'] += 1
                    except:
                        pass
            
            # 完全对齐检查
            if all(alignment.values()):
                alignment_stats['fully_aligned'] += 1
            else:
                alignment_stats['missing_details'][block_id] = {
                    k: v for k, v in alignment.items() if not v
                }
        
        total_regions = len(self.all_block_ids)
        print(f"📊 数据对齐统计:")
        print(f"   - 知识图谱对齐: {alignment_stats['kg_aligned']}/{total_regions} ({alignment_stats['kg_aligned']/total_regions:.1%})")
        print(f"   - 能耗标签对齐: {alignment_stats['energy_aligned']}/{total_regions} ({alignment_stats['energy_aligned']/total_regions:.1%})")
        print(f"   - 完全对齐: {alignment_stats['fully_aligned']}/{total_regions} ({alignment_stats['fully_aligned']/total_regions:.1%})")
        
        if alignment_stats['energy_aligned'] < total_regions * 0.9:
            self.issues.append("能耗标签对齐率低于90%")
            self.recommendations.append("检查region_info文件的key格式和能耗字段")
        
        if alignment_stats['kg_aligned'] < total_regions * 0.9:
            self.issues.append("知识图谱实体对齐率低于90%")
            self.recommendations.append("检查知识图谱中的区域实体命名格式")
        
        self.results['alignment_check'] = alignment_stats
    
    def _analyze_data_quality(self):
        """分析数据质量"""
        print("\n📊 6. 数据质量分析")
        print("-" * 50)
        
        quality_stats = {
            'energy_quality': {},
            'missing_data': {},
            'outliers': {},
            'data_types': {}
        }
        
        # 能耗数据质量分析
        all_energies = []
        energy_types = defaultdict(int)
        missing_energy = 0
        zero_energy = 0
        
        for key, data in self.region_info.items():
            if isinstance(data, dict):
                if 'energy' in data:
                    energy_val = data['energy']
                    energy_types[type(energy_val).__name__] += 1
                    
                    if energy_val is None:
                        missing_energy += 1
                    else:
                        try:
                            float_val = float(energy_val)
                            all_energies.append(float_val)
                            if float_val == 0.0:
                                zero_energy += 1
                        except:
                            missing_energy += 1
                else:
                    missing_energy += 1
        
        if all_energies:
            energy_array = np.array(all_energies)
            
            # 异常值检测
            q1 = np.percentile(energy_array, 25)
            q3 = np.percentile(energy_array, 75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = (energy_array < lower_bound) | (energy_array > upper_bound)
            outlier_count = np.sum(outliers)
            
            quality_stats['energy_quality'] = {
                'total_regions': len(self.region_info),
                'valid_energy_count': len(all_energies),
                'missing_energy_count': missing_energy,
                'zero_energy_count': zero_energy,
                'energy_range': [float(energy_array.min()), float(energy_array.max())],
                'energy_mean': float(energy_array.mean()),
                'energy_std': float(energy_array.std()),
                'outlier_count': int(outlier_count),
                'outlier_percentage': float(outlier_count / len(all_energies) * 100),
                'energy_types': dict(energy_types)
            }
            
            print(f"📈 能耗数据质量:")
            print(f"   - 有效能耗数量: {len(all_energies)}/{len(self.region_info)}")
            print(f"   - 缺失能耗数量: {missing_energy}")
            print(f"   - 零值能耗数量: {zero_energy}")
            print(f"   - 能耗范围: [{energy_array.min():.3f}, {energy_array.max():.3f}]")
            print(f"   - 能耗均值: {energy_array.mean():.3f} ± {energy_array.std():.3f}")
            print(f"   - 异常值数量: {outlier_count} ({outlier_count/len(all_energies)*100:.1f}%)")
            
            if missing_energy > len(self.region_info) * 0.1:
                self.issues.append(f"缺失能耗标签过多: {missing_energy}")
                self.recommendations.append("检查数据收集过程，补充缺失的能耗标签")
            
            if outlier_count > len(all_energies) * 0.1:
                self.issues.append(f"能耗异常值过多: {outlier_count}")
                self.recommendations.append("考虑异常值处理或数据清洗")
        
        self.results['quality_analysis'] = quality_stats
    
    def _analyze_data_distribution(self):
        """分析数据分布"""
        print("\n📊 7. 数据分布分析")
        print("-" * 50)
        
        def get_energy_stats(block_ids, set_name):
            """获取指定区域集合的能耗统计"""
            energies = []
            for block_id in block_ids:
                key = str(block_id)
                if key in self.region_info and 'energy' in self.region_info[key]:
                    try:
                        energy = float(self.region_info[key]['energy'])
                        energies.append(energy)
                    except:
                        pass
            
            if energies:
                energies = np.array(energies)
                return {
                    'count': len(energies),
                    'mean': float(energies.mean()),
                    'std': float(energies.std()),
                    'min': float(energies.min()),
                    'max': float(energies.max()),
                    'median': float(np.median(energies)),
                    'q25': float(np.percentile(energies, 25)),
                    'q75': float(np.percentile(energies, 75)),
                    'values': energies.tolist()
                }
            return {'count': 0, 'values': []}
        
        train_stats = get_energy_stats(self.train_df['BlockID'].tolist(), 'train')
        val_stats = get_energy_stats(self.val_df['BlockID'].tolist(), 'val')
        test_stats = get_energy_stats(self.test_df['BlockID'].tolist(), 'test')
        
        distribution_stats = {
            'train': train_stats,
            'val': val_stats,
            'test': test_stats
        }
        
        print(f"📈 各数据集能耗分布:")
        for set_name, stats in distribution_stats.items():
            if stats['count'] > 0:
                print(f"   - {set_name.upper()}集: 数量={stats['count']}, 均值={stats['mean']:.3f}, 标准差={stats['std']:.3f}")
                print(f"     范围=[{stats['min']:.3f}, {stats['max']:.3f}], 中位数={stats['median']:.3f}")
        
        # 分布差异分析
        if train_stats['count'] > 0 and test_stats['count'] > 0:
            mean_diff = abs(train_stats['mean'] - test_stats['mean'])
            relative_diff = mean_diff / train_stats['mean'] * 100
            
            print(f"\n🎯 训练集 vs 测试集分布差异:")
            print(f"   - 均值差异: {mean_diff:.3f} ({relative_diff:.1f}%)")
            
            if relative_diff > 20:
                self.issues.append(f"训练集和测试集均值差异过大: {relative_diff:.1f}%")
                self.recommendations.append("考虑重新划分数据集或使用分层采样")
            elif relative_diff > 10:
                self.issues.append(f"训练集和测试集均值差异较大: {relative_diff:.1f}%")
                self.recommendations.append("监控模型泛化能力，可能需要域适应")
        
        # 样本量检查
        if test_stats['count'] < 20:
            self.issues.append(f"测试集样本量过小: {test_stats['count']}")
            self.recommendations.append("增加测试集样本量或使用交叉验证")
        
        self.results['distribution_analysis'] = distribution_stats
    
    def _check_image_availability(self):
        """检查图像数据可用性"""
        print("\n🖼️ 8. 图像数据可用性检查") 
        print("-" * 50)
        
        print("⚠️ 图像数据检查功能需要具体的图像目录配置")
        print("建议在config.py中添加图像目录路径配置")
        
        self.results['image_availability'] = {
            'status': 'skipped',
            'reason': 'image directory not configured'
        }
    
    def _analyze_model_predictions(self):
        """分析模型预测结果"""
        print("\n🔍 9. 模型预测结果分析")
        print("-" * 50)
        
        results_path = "./saved_models/shenyang_RGCN_results.json"
        
        if Path(results_path).exists():
            try:
                with open(results_path, 'r') as f:
                    model_results = json.load(f)
                
                # 修复: 安全地转换预测结果
                try:
                    if isinstance(model_results['predictions'], list):
                        predictions = np.array(model_results['predictions'], dtype=float)
                    else:
                        predictions = np.array(model_results['predictions'])
                    
                    if isinstance(model_results['labels'], list):
                        labels = np.array(model_results['labels'], dtype=float)
                    else:
                        labels = np.array(model_results['labels'])
                    
                    # 计算详细指标
                    from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
                    
                    r2 = r2_score(labels, predictions)
                    rmse = np.sqrt(mean_squared_error(labels, predictions))
                    mae = mean_absolute_error(labels, predictions)
                    
                    # 基准模型分析
                    mean_pred = np.full_like(labels, labels.mean())
                    r2_baseline = r2_score(labels, mean_pred)
                    
                    # 预测偏差分析
                    bias = predictions.mean() - labels.mean()
                    correlation = np.corrcoef(predictions, labels)[0, 1]
                    
                    prediction_analysis = {
                        'r2_score': float(r2),
                        'rmse': float(rmse),
                        'mae': float(mae),
                        'r2_baseline': float(r2_baseline),
                        'prediction_bias': float(bias),
                        'correlation': float(correlation),
                        'predictions_range': [float(predictions.min()), float(predictions.max())],
                        'labels_range': [float(labels.min()), float(labels.max())],
                        'predictions_mean': float(predictions.mean()),
                        'labels_mean': float(labels.mean())
                    }
                    
                    print(f"📊 模型预测分析:")
                    print(f"   - R²得分: {r2:.4f}")
                    print(f"   - 基准R²: {r2_baseline:.4f}")
                    print(f"   - RMSE: {rmse:.4f}")
                    print(f"   - MAE: {mae:.4f}")
                    print(f"   - 预测偏差: {bias:.4f}")
                    print(f"   - 相关系数: {correlation:.4f}")
                    
                    # R²负数问题诊断
                    if r2 < 0:
                        self.issues.append(f"R²为负数: {r2:.4f}")
                        if r2 < r2_baseline:
                            self.recommendations.append("模型性能比基准差，考虑简化模型或增加正则化")
                        if abs(bias) > labels.std() * 0.5:
                            self.recommendations.append(f"预测偏差过大 ({bias:.3f})，检查模型训练过程")
                        if correlation < 0.3:
                            self.recommendations.append(f"预测相关性过低 ({correlation:.3f})，模型可能未学到有效模式")
                    
                    self.results['prediction_analysis'] = prediction_analysis
                    
                except Exception as e:
                    print(f"❌ 预测结果解析失败: {e}")
                    print(f"   - 预测数据类型: {type(model_results.get('predictions', 'N/A'))}")
                    print(f"   - 标签数据类型: {type(model_results.get('labels', 'N/A'))}")
            
            except Exception as e:
                print(f"❌ 模型结果文件读取失败: {e}")
        else:
            print(f"⚠️  未找到模型结果文件: {results_path}")
            self.recommendations.append("先运行模型训练生成预测结果")
    
    def _diagnose_and_recommend(self):
        """问题诊断和解决方案推荐"""
        print("\n💡 10. 问题诊断和解决方案")
        print("-" * 50)
        
        print(f"🚨 发现的问题 ({len(self.issues)}):")
        for i, issue in enumerate(self.issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n💡 推荐的解决方案 ({len(self.recommendations)}):")
        for i, rec in enumerate(self.recommendations, 1):
            print(f"   {i}. {rec}")
        
        # 优先级排序
        priority_recommendations = []
        
        # 高优先级：影响模型性能的核心问题
        for issue in self.issues:
            if "R²为负数" in issue:
                priority_recommendations.append("立即修复：增加正则化，减少过拟合")
            elif "样本量过小" in issue:
                priority_recommendations.append("立即修复：使用交叉验证或增加数据")
            elif "均值差异过大" in issue:
                priority_recommendations.append("立即修复：重新划分数据集")
        
        # 中优先级：数据质量问题
        for issue in self.issues:
            if "缺失" in issue or "异常值" in issue:
                priority_recommendations.append("中等优先级：数据清洗和预处理")
            elif "连通性" in issue:
                priority_recommendations.append("中等优先级：完善知识图谱结构")
        
        if priority_recommendations:
            print(f"\n🎯 优先级解决方案:")
            for i, rec in enumerate(priority_recommendations, 1):
                print(f"   {i}. {rec}")
        
        self.results['diagnosis'] = {
            'total_issues': len(self.issues),
            'issues': self.issues,
            'recommendations': self.recommendations,
            'priority_recommendations': priority_recommendations
        }
    
    def _generate_enhanced_visualizations(self):
        """生成增强的可视化报告"""
        print("\n📊 11. 生成增强可视化报告")
        print("-" * 50)
        
        try:
            self._create_comprehensive_plots()
            self._create_kg_analysis_plots()
            self._create_anomaly_detection_plots()
            print("✅ 增强可视化报告已生成")
        except Exception as e:
            print(f"⚠️  可视化生成失败: {e}")
    
    def _create_comprehensive_plots(self):
        """创建综合可视化图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans'] 
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建主报告图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('沈阳城市能耗数据集综合质量分析报告 (增强版)', fontsize=16, fontweight='bold')
        
        # 1. 数据对齐情况
        ax1 = axes[0, 0]
        alignment = self.results.get('alignment_check', {})
        categories = ['知识图谱', '能耗标签']
        values = [
            alignment.get('kg_aligned', 0),
            alignment.get('energy_aligned', 0)
        ]
        total = self.results['data_loading']['total_unique_blocks']
        rates = [v/total for v in values]
        
        bars = ax1.bar(categories, rates, color=['#FF6B6B', '#4ECDC4'])
        ax1.set_title('数据对齐完整性')
        ax1.set_ylabel('对齐率')
        ax1.set_ylim(0, 1)
        for bar, rate in zip(bars, rates):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{rate:.1%}', ha='center', va='bottom')
        
        # 2. 能耗分布对比
        ax2 = axes[0, 1]
        dist_stats = self.results.get('distribution_analysis', {})
        train_energies = dist_stats.get('train', {}).get('values', [])
        test_energies = dist_stats.get('test', {}).get('values', [])
        
        if train_energies and test_energies:
            ax2.hist(train_energies, bins=15, alpha=0.7, label=f'训练集 (n={len(train_energies)})', color='blue')
            ax2.hist(test_energies, bins=15, alpha=0.7, label=f'测试集 (n={len(test_energies)})', color='red')
            ax2.set_title('训练集 vs 测试集能耗分布')
            ax2.set_xlabel('能耗值')
            ax2.set_ylabel('频数')
            ax2.legend()
        
        # 3. 知识图谱结构统计
        ax3 = axes[0, 2]
        kg_metrics = self.results.get('kg_analysis_enhanced', {})
        basic_stats = kg_metrics.get('basic_stats', {})
        if basic_stats:
            metrics = ['节点数', '边数', '平均度']
            values = [
                basic_stats.get('nodes', 0),
                basic_stats.get('edges', 0),
                basic_stats.get('avg_degree', 0) * 100  # 缩放用于显示
            ]
            ax3.bar(metrics, values, color=['#4ECDC4', '#FF6B6B', '#45B7D1'])
            ax3.set_title('知识图谱结构统计')
            ax3.set_ylabel('数量')
            ax3.set_yscale('log')
        
        # 4. 异常检测结果
        ax4 = axes[1, 0]
        anomaly_results = self.results.get('feature_anomaly_detection', {})
        if anomaly_results:
            methods = ['统计异常', '孤立森林', '聚类异常']
            counts = [
                len(anomaly_results.get('statistical_anomalies', {})),
                anomaly_results.get('isolation_forest', {}).get('anomaly_count', 0),
                anomaly_results.get('dbscan_clustering', {}).get('noise_count', 0)
            ]
            ax4.bar(methods, counts, color=['#FFD93D', '#96CEB4', '#FECA57'])
            ax4.set_title('异常检测结果')
            ax4.set_ylabel('异常数量')
        
        # 5. 实体类型分布
        ax5 = axes[1, 1]
        entity_types = kg_metrics.get('entity_types', {})
        if entity_types:
            top_types = sorted(entity_types.items(), key=lambda x: x[1], reverse=True)[:5]
            types, counts = zip(*top_types)
            ax5.pie(counts, labels=types, autopct='%1.1f%%', startangle=90)
            ax5.set_title('实体类型分布 (Top 5)')
        
        # 6. 问题严重程度
        ax6 = axes[1, 2]
        issues = self.results.get('diagnosis', {}).get('issues', [])
        issue_types = {'数据质量': 0, '分布差异': 0, '对齐问题': 0, '模型性能': 0, '图谱结构': 0}
        
        for issue in issues:
            if 'R²' in issue or '性能' in issue:
                issue_types['模型性能'] += 1
            elif '差异' in issue or '分布' in issue:
                issue_types['分布差异'] += 1
            elif '对齐' in issue or '匹配' in issue:
                issue_types['对齐问题'] += 1
            elif '连通' in issue or '图谱' in issue:
                issue_types['图谱结构'] += 1
            else:
                issue_types['数据质量'] += 1
        
        issue_categories = list(issue_types.keys())
        issue_counts = list(issue_types.values())
        ax6.bar(issue_categories, issue_counts, color=['#FF6B6B', '#FFD93D', '#96CEB4', '#45B7D1', '#FECA57'])
        ax6.set_title('发现问题类型分布')
        ax6.set_ylabel('问题数量')
        plt.setp(ax6.get_xticklabels(), rotation=45, ha='right')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'comprehensive_quality_analysis_enhanced.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_kg_analysis_plots(self):
        """创建知识图谱分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('知识图谱深度分析', fontsize=16, fontweight='bold')
        
        kg_metrics = self.results.get('kg_analysis_enhanced', {})
        
        # 1. 关系类型分布
        ax1 = axes[0, 0]
        relation_types = kg_metrics.get('relation_types', {})
        if relation_types:
            top_relations = sorted(relation_types.items(), key=lambda x: x[1], reverse=True)[:10]
            relations, counts = zip(*top_relations)
            ax1.barh(relations, counts, color='skyblue')
            ax1.set_title('关系类型分布 (Top 10)')
            ax1.set_xlabel('频次')
        
        # 2. 实体类型详细分布
        ax2 = axes[0, 1]
        entity_types = kg_metrics.get('entity_types', {})
        if entity_types:
            types = list(entity_types.keys())
            counts = list(entity_types.values())
            ax2.bar(types, counts, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'])
            ax2.set_title('实体类型详细分布')
            ax2.set_ylabel('数量')
            plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
        
        # 3. 区域连接度分布
        ax3 = axes[1, 0]
        region_connections = kg_metrics.get('region_connections', {})
        if region_connections:
            connections = list(region_connections.values())
            ax3.hist(connections, bins=20, color='lightgreen', alpha=0.7, edgecolor='black')
            ax3.set_title('区域连接度分布')
            ax3.set_xlabel('连接度')
            ax3.set_ylabel('区域数量')
            ax3.axvline(np.mean(connections), color='red', linestyle='--', label=f'平均值: {np.mean(connections):.1f}')
            ax3.legend()
        
        # 4. 图谱连通性统计
        ax4 = axes[1, 1]
        connectivity = kg_metrics.get('connectivity', {})
        if connectivity:
            labels = ['强连通', '弱连通']
            values = [
                1 if connectivity.get('is_strongly_connected') else 0,
                1 if not connectivity.get('is_strongly_connected') else 0
            ]
            colors = ['green' if connectivity.get('is_strongly_connected') else 'red', 
                     'red' if not connectivity.get('is_strongly_connected') else 'green']
            ax4.pie(values, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax4.set_title('图谱连通性状态')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'knowledge_graph_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_anomaly_detection_plots(self):
        """创建异常检测图表"""
        anomaly_results = self.results.get('feature_anomaly_detection', {})
        if not anomaly_results:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('特征异常检测分析', fontsize=16, fontweight='bold')
        
        # 1. 统计异常分布
        ax1 = axes[0, 0]
        statistical_anomalies = anomaly_results.get('statistical_anomalies', {})
        if statistical_anomalies:
            features = list(statistical_anomalies.keys())
            percentages = [info['percentage'] for info in statistical_anomalies.values()]
            ax1.bar(features, percentages, color='orange', alpha=0.7)
            ax1.set_title('各特征统计异常比例')
            ax1.set_ylabel('异常比例 (%)')
            ax1.axhline(y=10, color='red', linestyle='--', label='10%阈值')
            ax1.legend()
            plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
        
        # 2. 异常检测方法对比
        ax2 = axes[0, 1]
        methods = ['孤立森林', 'DBSCAN聚类', '多方法一致']
        counts = [
            len(anomaly_results.get('isolation_forest', {}).get('anomaly_indices', [])),
            anomaly_results.get('dbscan_clustering', {}).get('noise_count', 0),
            anomaly_results.get('consistent_anomalies', {}).get('count', 0)
        ]
        ax2.bar(methods, counts, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax2.set_title('异常检测方法对比')
        ax2.set_ylabel('异常样本数')
        
        # 3. 高相关性特征对
        ax3 = axes[1, 0]
        high_corr_pairs = anomaly_results.get('high_correlation_pairs', [])
        if high_corr_pairs:
            pair_names = [f"{pair[0][:10]}↔{pair[1][:10]}" for pair in high_corr_pairs[:10]]
            correlations = [abs(pair[2]) for pair in high_corr_pairs[:10]]
            ax3.barh(pair_names, correlations, color='purple', alpha=0.7)
            ax3.set_title('高相关性特征对')
            ax3.set_xlabel('相关系数绝对值')
            ax3.axvline(x=0.9, color='red', linestyle='--', label='0.9阈值')
            ax3.legend()
        else:
            ax3.text(0.5, 0.5, '未发现高相关性特征对', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('高相关性特征对')
        
        # 4. 异常样本比例汇总
        ax4 = axes[1, 1]
        total_samples = anomaly_results.get('valid_region_count', 1)
        anomaly_counts = {
            '孤立森林': len(anomaly_results.get('isolation_forest', {}).get('anomaly_indices', [])),
            'DBSCAN': anomaly_results.get('dbscan_clustering', {}).get('noise_count', 0),
            '多方法一致': anomaly_results.get('consistent_anomalies', {}).get('count', 0)
        }
        
        anomaly_percentages = {k: v/total_samples*100 for k, v in anomaly_counts.items()}
        
        methods = list(anomaly_percentages.keys())
        percentages = list(anomaly_percentages.values())
        ax4.pie(percentages, labels=methods, autopct='%1.1f%%', startangle=90)
        ax4.set_title('异常样本比例分布')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'anomaly_detection_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_fix_code(self):
        """生成修复代码"""
        print("\n🔧 12. 生成修复代码和配置")
        print("-" * 50)
        
        # 基于诊断结果生成修复建议代码
        fix_code = self._create_fix_code_content()
        
        # 保存修复代码
        fix_code_path = self.output_dir / 'recommended_fixes_enhanced.py'
        with open(fix_code_path, 'w', encoding='utf-8') as f:
            f.write(fix_code)
        
        print(f"✅ 增强修复代码已生成: {fix_code_path}")
    
    def _create_fix_code_content(self):
        """创建修复代码内容"""
        issues = self.results.get('diagnosis', {}).get('issues', [])
        anomaly_results = self.results.get('feature_anomaly_detection', {})
        kg_metrics = self.results.get('kg_analysis_enhanced', {})
        
        code_template = '''#!/usr/bin/env python3
"""
基于数据质量检查的自动修复建议代码 (增强版)

生成时间: {timestamp}
检测到的问题: {issue_count} 个
知识图谱问题: {kg_issues}
异常检测结果: {anomaly_summary}
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest

# 增强修复建议配置
ENHANCED_RECOMMENDED_CONFIG = {{
'''
        
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 统计问题类型
        kg_issues = []
        for issue in issues:
            if any(word in issue for word in ['连通', '图谱', '关系', '实体']):
                kg_issues.append(issue)
        
        anomaly_summary = "未检测到异常" if not anomaly_results else f"检测到{anomaly_results.get('consistent_anomalies', {}).get('count', 0)}个一致异常"
        
        # 根据问题生成具体的修复配置
        config_fixes = []
        
        # 知识图谱修复配置
        if kg_issues:
            config_fixes.append('''
    # 知识图谱结构修复
    "knowledge_graph_fixes": {
        "improve_connectivity": True,
        "add_missing_relations": True,
        "entity_embedding_dim": 64,
        "relation_embedding_dim": 32,
        "use_graph_attention": True
    },''')
        
        # 异常值处理配置
        if anomaly_results and anomaly_results.get('consistent_anomalies', {}).get('count', 0) > 0:
            config_fixes.append('''
    # 异常值处理配置
    "anomaly_handling": {
        "use_robust_scaler": True,
        "outlier_detection_method": "isolation_forest",
        "contamination_rate": 0.1,
        "handle_anomalies": "clip",  # 'clip', 'remove', 'impute'
        "feature_selection": True
    },''')
        
        # 模型性能修复
        for issue in issues:
            if "R²为负数" in issue:
                config_fixes.append('''
    # 模型性能修复
    "model_performance_fixes": {
        "increase_regularization": True,
        "weight_decay": 0.01,
        "dropout_rates": [0.5, 0.3, 0.2],
        "early_stopping_patience": 10,
        "reduce_learning_rate": True,
        "learning_rate": 0.0001,
        "use_batch_norm": True,
        "gradient_clipping": 1.0
    },''')
            
            if "样本量过小" in issue:
                config_fixes.append('''
    # 小样本处理
    "small_sample_fixes": {
        "use_cross_validation": True,
        "cv_folds": 5,
        "bootstrap_samples": True,
        "data_augmentation": True,
        "transfer_learning": True
    },''')
            
            if "均值差异" in issue:
                config_fixes.append('''
    # 分布差异修复
    "distribution_fixes": {
        "standardize_targets": True,
        "stratified_split": True,
        "domain_adaptation": True,
        "use_adversarial_training": False
    },''')
        
        # 特征工程配置
        if anomaly_results:
            config_fixes.append('''
    # 特征工程配置
    "feature_engineering": {
        "remove_high_correlation": True,
        "correlation_threshold": 0.9,
        "feature_selection_method": "mutual_info",
        "pca_components": 0.95,
        "polynomial_features": False
    },''')
        
        config_content = ''.join(config_fixes)
        if not config_content:
            config_content = '''
    # 基础优化配置
    "basic_optimization": {
        "batch_size": 16,
        "learning_rate": 0.001,
        "epochs": 50
    },'''
        
        final_code = code_template.format(
            timestamp=timestamp,
            issue_count=len(issues),
            kg_issues=len(kg_issues),
            anomaly_summary=anomaly_summary
        ) + config_content + '''
}

class DataQualityFixer:
    """数据质量修复器"""
    
    def __init__(self, config):
        self.config = config
        self.scalers = {}
        self.anomaly_detectors = {}
    
    def fix_anomalies(self, X, feature_names):
        """修复异常值"""
        if not self.config.get("anomaly_handling", {}).get("use_robust_scaler"):
            return X
        
        print("🔧 修复异常值...")
        
        # 使用鲁棒缩放器
        scaler = RobustScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 异常值检测
        if self.config["anomaly_handling"]["outlier_detection_method"] == "isolation_forest":
            detector = IsolationForest(
                contamination=self.config["anomaly_handling"]["contamination_rate"],
                random_state=42
            )
            anomaly_labels = detector.fit_predict(X_scaled)
            
            # 处理异常值
            handle_method = self.config["anomaly_handling"]["handle_anomalies"]
            if handle_method == "clip":
                # 裁剪异常值
                for i in range(X.shape[1]):
                    q1, q3 = np.percentile(X[:, i], [25, 75])
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    X[:, i] = np.clip(X[:, i], lower_bound, upper_bound)
        
        return X
    
    def fix_feature_correlation(self, X, feature_names):
        """修复特征高相关性"""
        if not self.config.get("feature_engineering", {}).get("remove_high_correlation"):
            return X, feature_names
        
        print("🔧 修复特征高相关性...")
        
        # 计算相关系数矩阵
        corr_matrix = np.corrcoef(X.T)
        threshold = self.config["feature_engineering"]["correlation_threshold"]
        
        # 找出高相关特征
        to_remove = set()
        for i in range(len(feature_names)):
            for j in range(i+1, len(feature_names)):
                if abs(corr_matrix[i, j]) > threshold:
                    # 移除方差较小的特征
                    if np.var(X[:, i]) < np.var(X[:, j]):
                        to_remove.add(i)
                    else:
                        to_remove.add(j)
        
        # 移除高相关特征
        if to_remove:
            keep_indices = [i for i in range(len(feature_names)) if i not in to_remove]
            X_filtered = X[:, keep_indices]
            feature_names_filtered = [feature_names[i] for i in keep_indices]
            print(f"   移除了 {len(to_remove)} 个高相关特征")
            return X_filtered, feature_names_filtered
        
        return X, feature_names
    
    def apply_model_fixes(self, model):
        """应用模型修复"""
        if not self.config.get("model_performance_fixes"):
            return model
        
        print("🔧 应用模型性能修复...")
        
        fixes = self.config["model_performance_fixes"]
        
        # 添加正则化
        if fixes.get("increase_regularization"):
            print("   ✅ 增加L2正则化")
        
        # 添加Dropout
        if fixes.get("dropout_rates"):
            print(f"   ✅ 添加Dropout层: {fixes['dropout_rates']}")
        
        # 批量归一化
        if fixes.get("use_batch_norm"):
            print("   ✅ 添加批量归一化")
        
        return model


def apply_enhanced_fixes():
    """应用增强修复建议"""
    print("🔧 应用增强数据质量修复...")
    
    fixer = DataQualityFixer(ENHANCED_RECOMMENDED_CONFIG)
    
    # 1. 知识图谱修复
    if ENHANCED_RECOMMENDED_CONFIG.get("knowledge_graph_fixes"):
        print("   ✅ 知识图谱结构优化")
    
    # 2. 异常值处理
    if ENHANCED_RECOMMENDED_CONFIG.get("anomaly_handling"):
        print("   ✅ 异常值检测和处理")
    
    # 3. 特征工程
    if ENHANCED_RECOMMENDED_CONFIG.get("feature_engineering"):
        print("   ✅ 特征工程优化")
    
    # 4. 模型性能修复  
    if ENHANCED_RECOMMENDED_CONFIG.get("model_performance_fixes"):
        print("   ✅ 模型架构和训练优化")
    
    # 5. 数据分布修复
    if ENHANCED_RECOMMENDED_CONFIG.get("distribution_fixes"):
        print("   ✅ 数据分布对齐")
    
    return ENHANCED_RECOMMENDED_CONFIG, fixer

if __name__ == "__main__":
    config, fixer = apply_enhanced_fixes()
    print("🎉 增强修复配置生成完成!")
    print("📋 下一步操作:")
    print("   1. 将配置集成到主训练脚本")
    print("   2. 运行数据预处理和清洗")
    print("   3. 重新训练模型验证效果")
'''
        
        return final_code
    
    def _save_comprehensive_report(self):
        """保存综合报告"""
        print("\n💾 13. 保存综合报告")
        print("-" * 50)
        
        # 1. 保存JSON报告
        json_path = self.output_dir / 'comprehensive_quality_report_enhanced.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        # 2. 生成Markdown报告
        md_path = self.output_dir / 'quality_analysis_report_enhanced.md'
        markdown_content = self._generate_markdown_report()
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ 增强综合报告已保存")
        print(f"   - JSON报告: {json_path}")
        print(f"   - Markdown报告: {md_path}")
        print(f"   - 综合分析图: comprehensive_quality_analysis_enhanced.png")
        print(f"   - 知识图谱分析图: knowledge_graph_analysis.png")
        print(f"   - 异常检测分析图: anomaly_detection_analysis.png")
        print(f"   - 增强修复代码: recommended_fixes_enhanced.py")
        if (self.output_dir / 'anomalous_regions_detail.json').exists():
            print(f"   - 异常区域详情: anomalous_regions_detail.json")
    
    def _generate_markdown_report(self):
        """生成Markdown报告"""
        kg_metrics = self.results.get('kg_analysis_enhanced', {})
        anomaly_results = self.results.get('feature_anomaly_detection', {})
        
        return f"""# 沈阳城市能耗数据集综合质量分析报告 (增强版)

## 📊 数据集概览

- **总区域数**: {self.results['data_loading']['total_unique_blocks']}
- **训练集**: {self.results['data_loading']['train_count']} 个区域
- **验证集**: {self.results['data_loading']['val_count']} 个区域  
- **测试集**: {self.results['data_loading']['test_count']} 个区域

## 🕸️ 知识图谱深度分析

### 基础统计
- **节点数**: {kg_metrics.get('basic_stats', {}).get('nodes', 0):,}
- **边数**: {kg_metrics.get('basic_stats', {}).get('edges', 0):,}
- **平均度**: {kg_metrics.get('basic_stats', {}).get('avg_degree', 0):.2f}

### 连通性分析
- **强连通性**: {'是' if kg_metrics.get('connectivity', {}).get('is_strongly_connected') else '否'}
- **强连通分量数**: {kg_metrics.get('connectivity', {}).get('num_scc', 0)}

### 实体类型分布
{self._format_entity_types(kg_metrics.get('entity_types', {}))}

## 🔍 特征异常检测结果

### 异常检测统计
- **有效样本数**: {anomaly_results.get('valid_region_count', 0)}
- **孤立森林异常**: {anomaly_results.get('isolation_forest', {}).get('anomaly_count', 0)} 个
- **聚类异常**: {anomaly_results.get('dbscan_clustering', {}).get('noise_count', 0)} 个
- **多方法一致异常**: {anomaly_results.get('consistent_anomalies', {}).get('count', 0)} 个

### 高相关性特征对
{self._format_correlation_pairs(anomaly_results.get('high_correlation_pairs', []))}

## 🔗 数据对齐完整性

- **能耗标签对齐率**: {self.results.get('alignment_check', {}).get('energy_aligned', 0)}/{self.results['data_loading']['total_unique_blocks']} ({self.results.get('alignment_check', {}).get('energy_aligned', 0)/self.results['data_loading']['total_unique_blocks']:.1%})
- **知识图谱对齐率**: {self.results.get('alignment_check', {}).get('kg_aligned', 0)}/{self.results['data_loading']['total_unique_blocks']} ({self.results.get('alignment_check', {}).get('kg_aligned', 0)/self.results['data_loading']['total_unique_blocks']:.1%})

## 📈 数据质量分析

{self._format_quality_stats()}

## 🚨 发现的问题

{self._format_issues()}

## 💡 推荐解决方案

{self._format_recommendations()}

## 🎯 立即行动计划

1. **运行增强修复代码**: `python recommended_fixes_enhanced.py`
2. **处理异常区域**: 查看 `anomalous_regions_detail.json`
3. **优化知识图谱**: 改善图谱连通性和关系多样性
4. **应用特征工程**: 处理高相关性特征
5. **重新训练模型**: 使用修复后的配置

---

*增强报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    def _format_entity_types(self, entity_types):
        """格式化实体类型"""
        if not entity_types:
            return "无数据"
        
        total = sum(entity_types.values())
        result = []
        for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total * 100
            result.append(f"- **{entity_type}**: {count:,} ({percentage:.1f}%)")
        
        return '\n'.join(result)
    
    def _format_correlation_pairs(self, pairs):
        """格式化相关性特征对"""
        if not pairs:
            return "未发现高相关性特征对"
        
        result = []
        for feat1, feat2, corr in pairs[:5]:  # 显示前5对
            result.append(f"- **{feat1}** ↔ **{feat2}**: {corr:.3f}")
        
        return '\n'.join(result)
    
    def _format_quality_stats(self):
        """格式化质量统计"""
        quality = self.results.get('quality_analysis', {}).get('energy_quality', {})
        if quality:
            return f"""
- **有效能耗标签**: {quality.get('valid_energy_count', 0)}
- **能耗范围**: [{quality.get('energy_range', [0, 0])[0]:.3f}, {quality.get('energy_range', [0, 0])[1]:.3f}]
- **能耗均值**: {quality.get('energy_mean', 0):.3f} ± {quality.get('energy_std', 0):.3f}
- **异常值比例**: {quality.get('outlier_percentage', 0):.1f}%
"""
        return "数据质量信息不可用"
    
    def _format_issues(self):
        """格式化问题列表"""
        issues = self.results.get('diagnosis', {}).get('issues', [])
        if issues:
            return '\n'.join([f"{i}. {issue}" for i, issue in enumerate(issues, 1)])
        return "未发现严重问题"
    
    def _format_recommendations(self):
        """格式化建议列表"""
        recs = self.results.get('diagnosis', {}).get('recommendations', [])
        if recs:
            return '\n'.join([f"{i}. {rec}" for i, rec in enumerate(recs, 1)])
        return "暂无特别建议"


def main():
    """主函数"""
    print("🚀 启动统一数据质量检查工具 (增强版)...")
    
    checker = UnifiedDataQualityChecker()
    results = checker.run_comprehensive_check()
    
    print(f"\n🎉 增强综合检查完成!")
    print(f"📁 详细报告已保存到: {checker.output_dir}")
    print(f"🔍 请查看以下文件:")
    print(f"   - comprehensive_quality_report_enhanced.json (详细数据)")
    print(f"   - quality_analysis_report_enhanced.md (增强分析报告)")
    print(f"   - comprehensive_quality_analysis_enhanced.png (综合分析图表)")
    print(f"   - knowledge_graph_analysis.png (知识图谱分析图)")
    print(f"   - anomaly_detection_analysis.png (异常检测分析图)")
    print(f"   - recommended_fixes_enhanced.py (增强修复代码)")
    print(f"   - anomalous_regions_detail.json (异常区域详情)")


if __name__ == "__main__":
    main()