#!/usr/bin/env python3
"""
知识图谱优化策略 - 提升RGCN模型R²性能
目标：在保持757个区域的同时，将R²从0.05提升到0.25左右

基于对比分析的优化方案：
- 知识图谱A：757区域，R²=0.05，连通性差
- 知识图谱B：122区域，R²=0.32，连通性好
"""

import json
import numpy as np
import pandas as pd
from collections import defaultdict, Counter
import networkx as nx
from pathlib import Path

class KnowledgeGraphOptimizer:
    """知识图谱优化器"""
    
    def __init__(self, region_info_path, original_kg_path, output_dir="./optimized_kg/"):
        self.region_info_path = region_info_path
        self.original_kg_path = original_kg_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 加载数据
        self.load_data()
        
        # 优化参数
        self.optimization_config = {
            'target_connectivity': 0.8,  # 目标连通性
            'min_region_connections': 50,  # 每个区域最少连接数
            'max_region_connections': 1000,  # 每个区域最多连接数
            'relation_balance_threshold': 0.6,  # 关系分布平衡阈值
            'spatial_connection_radius': 0.01,  # 空间连接半径
        }
    
    def load_data(self):
        """加载原始数据"""
        print("📋 加载原始数据...")
        
        # 加载区域信息
        with open(self.region_info_path, 'r', encoding='utf-8') as f:
            self.region_info = json.load(f)
        
        # 加载原始知识图谱
        self.original_triples = []
        with open(self.original_kg_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    self.original_triples.append((parts[0], parts[1], parts[2]))
        
        print(f"✅ 数据加载完成：{len(self.region_info)}个区域，{len(self.original_triples)}个三元组")
    
    def analyze_current_issues(self):
        """分析当前知识图谱的问题"""
        print("\n🔍 分析当前知识图谱问题...")
        
        # 构建图
        G = nx.DiGraph()
        region_connections = defaultdict(int)
        relation_counts = defaultdict(int)
        
        for head, relation, tail in self.original_triples:
            G.add_edge(head, tail, relation=relation)
            relation_counts[relation] += 1
            
            if head.startswith('Region_'):
                region_connections[head] += 1
            if tail.startswith('Region_'):
                region_connections[tail] += 1
        
        # 连通性分析
        scc = list(nx.strongly_connected_components(G))
        largest_scc_size = max(len(component) for component in scc) if scc else 0
        connectivity_ratio = largest_scc_size / G.number_of_nodes() if G.number_of_nodes() > 0 else 0
        
        # 区域连接度分析
        region_conn_values = list(region_connections.values())
        avg_connections = np.mean(region_conn_values) if region_conn_values else 0
        min_connections = min(region_conn_values) if region_conn_values else 0
        max_connections = max(region_conn_values) if region_conn_values else 0
        
        # 关系分布分析
        relation_values = list(relation_counts.values())
        relation_gini = self.calculate_gini_coefficient(relation_values) if len(relation_values) > 1 else 0
        
        issues = {
            'connectivity_ratio': connectivity_ratio,
            'avg_region_connections': avg_connections,
            'min_region_connections': min_connections,
            'max_region_connections': max_connections,
            'relation_gini': relation_gini,
            'num_relations': len(relation_counts),
            'total_nodes': G.number_of_nodes(),
            'total_edges': G.number_of_edges()
        }
        
        print(f"   - 连通性比例: {connectivity_ratio:.3f}")
        print(f"   - 区域平均连接度: {avg_connections:.1f}")
        print(f"   - 区域连接度范围: [{min_connections}, {max_connections}]")
        print(f"   - 关系分布基尼系数: {relation_gini:.3f}")
        
        return issues
    
    def calculate_gini_coefficient(self, values):
        """计算基尼系数"""
        values = np.array(values)
        values = np.sort(values)
        n = len(values)
        index = np.arange(1, n + 1)
        return (2 * np.sum(index * values)) / (n * np.sum(values)) - (n + 1) / n
    
    def generate_optimized_kg(self):
        """生成优化的知识图谱"""
        print("\n🚀 开始生成优化知识图谱...")
        
        optimized_triples = []
        
        # 1. 保留原有的高质量三元组
        print("   📋 1. 保留原有高质量关系...")
        high_quality_relations = {
            'cateOf', 'locateAt', 'hasFunction', 'hasPhysicalAttribute',
            'belongsToLand', 'nearBy', 'highConvenience'
        }
        
        for head, relation, tail in self.original_triples:
            if relation in high_quality_relations:
                optimized_triples.append((head, relation, tail))
        
        print(f"      保留了 {len(optimized_triples)} 个高质量三元组")
        
        # 2. 增强区域间连接
        print("   🔗 2. 增强区域间连接...")
        region_spatial_connections = self.create_spatial_connections()
        optimized_triples.extend(region_spatial_connections)
        print(f"      添加了 {len(region_spatial_connections)} 个空间连接")
        
        # 3. 添加功能相似性连接
        print("   🏢 3. 添加功能相似性连接...")
        functional_connections = self.create_functional_connections()
        optimized_triples.extend(functional_connections)
        print(f"      添加了 {len(functional_connections)} 个功能连接")
        
        # 4. 添加建筑物相关关系（借鉴知识图谱B的成功经验）
        print("   🏗️ 4. 添加建筑物相关关系...")
        building_connections = self.create_building_connections()
        optimized_triples.extend(building_connections)
        print(f"      添加了 {len(building_connections)} 个建筑物连接")
        
        # 5. 平衡关系分布
        print("   ⚖️ 5. 平衡关系分布...")
        balanced_triples = self.balance_relation_distribution(optimized_triples)
        
        print(f"✅ 优化完成：生成了 {len(balanced_triples)} 个三元组")
        
        return balanced_triples
    
    def create_spatial_connections(self):
        """创建空间邻近连接"""
        connections = []
        region_centers = {}
        
        # 提取区域中心点
        for region_id, region_data in self.region_info.items():
            if 'center' in region_data and isinstance(region_data['center'], list):
                region_centers[f"Region_{region_id}"] = region_data['center']
        
        # 计算空间邻近关系
        region_ids = list(region_centers.keys())
        for i, region1 in enumerate(region_ids):
            center1 = region_centers[region1]
            
            for j, region2 in enumerate(region_ids[i+1:], i+1):
                center2 = region_centers[region2]
                
                # 计算欧几里得距离
                distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
                
                if distance < self.optimization_config['spatial_connection_radius']:
                    connections.append((region1, 'spatiallyNear', region2))
                    connections.append((region2, 'spatiallyNear', region1))
        
        return connections
    
    def create_functional_connections(self):
        """创建功能相似性连接"""
        connections = []
        
        # 基于POI类别创建功能相似性
        region_poi_profiles = {}
        for region_id, region_data in self.region_info.items():
            if 'cate1_poi_number' in region_data:
                region_poi_profiles[f"Region_{region_id}"] = region_data['cate1_poi_number']
        
        # 计算功能相似性
        region_ids = list(region_poi_profiles.keys())
        for i, region1 in enumerate(region_ids):
            profile1 = np.array(region_poi_profiles[region1])
            
            for j, region2 in enumerate(region_ids[i+1:], i+1):
                profile2 = np.array(region_poi_profiles[region2])
                
                # 计算余弦相似度
                if np.linalg.norm(profile1) > 0 and np.linalg.norm(profile2) > 0:
                    similarity = np.dot(profile1, profile2) / (np.linalg.norm(profile1) * np.linalg.norm(profile2))
                    
                    if similarity > 0.7:  # 高相似度阈值
                        connections.append((region1, 'functionalSimilarity', region2))
                        connections.append((region2, 'functionalSimilarity', region1))
        
        return connections
    
    def create_building_connections(self):
        """创建建筑物相关连接（借鉴知识图谱B）"""
        connections = []
        
        # 为每个区域创建虚拟建筑物实体
        for region_id, region_data in self.region_info.items():
            region_entity = f"Region_{region_id}"
            
            # 基于POI数量创建建筑物
            n_pois = region_data.get('n_pois', 0)
            if n_pois > 0:
                # 创建建筑物实体
                building_entity = f"Building_{region_id}"
                connections.append((region_entity, 'hasBuilding', building_entity))
                connections.append((building_entity, 'withinRegion', region_entity))
                
                # 基于POI类别创建功能建筑物
                poi_categories = region_data.get('cate1_poi_number', [])
                for i, poi_count in enumerate(poi_categories):
                    if poi_count > 0:
                        func_building = f"FuncBuilding_{region_id}_{i}"
                        connections.append((building_entity, 'hasFunctionBuilding', func_building))
                        connections.append((func_building, 'belongsToBuilding', building_entity))
                        connections.append((func_building, 'hasFunction', f"Func_Category_{i}"))
        
        return connections

    def balance_relation_distribution(self, triples):
        """平衡关系分布"""
        relation_counts = defaultdict(int)
        for _, relation, _ in triples:
            relation_counts[relation] += 1

        # 计算目标分布
        total_triples = len(triples)
        target_per_relation = total_triples // len(relation_counts)

        balanced_triples = []
        relation_current_counts = defaultdict(int)

        # 优先保留重要关系
        important_relations = {
            'cateOf', 'locateAt', 'hasFunction', 'spatiallyNear',
            'functionalSimilarity', 'hasBuilding', 'hasFunctionBuilding'
        }

        for triple in triples:
            head, relation, tail = triple
            if (relation in important_relations or
                relation_current_counts[relation] < target_per_relation * 1.5):
                balanced_triples.append(triple)
                relation_current_counts[relation] += 1

        return balanced_triples

    def save_optimized_kg(self, optimized_triples):
        """保存优化后的知识图谱"""
        output_path = self.output_dir / "kg_optimized_for_rgcn.txt"

        with open(output_path, 'w', encoding='utf-8') as f:
            for head, relation, tail in optimized_triples:
                f.write(f"{head}\t{relation}\t{tail}\n")

        print(f"💾 优化知识图谱已保存到: {output_path}")

        # 生成统计报告
        self.generate_optimization_report(optimized_triples)

        return output_path

    def generate_optimization_report(self, optimized_triples):
        """生成优化报告"""
        print("\n📊 生成优化报告...")

        # 构建优化后的图
        G = nx.DiGraph()
        relation_counts = defaultdict(int)
        region_connections = defaultdict(int)

        for head, relation, tail in optimized_triples:
            G.add_edge(head, tail, relation=relation)
            relation_counts[relation] += 1

            if head.startswith('Region_'):
                region_connections[head] += 1
            if tail.startswith('Region_'):
                region_connections[tail] += 1

        # 计算优化后的指标
        scc = list(nx.strongly_connected_components(G))
        largest_scc_size = max(len(component) for component in scc) if scc else 0
        connectivity_ratio = largest_scc_size / G.number_of_nodes() if G.number_of_nodes() > 0 else 0

        region_conn_values = list(region_connections.values())
        avg_connections = np.mean(region_conn_values) if region_conn_values else 0

        relation_values = list(relation_counts.values())
        relation_gini = self.calculate_gini_coefficient(relation_values) if len(relation_values) > 1 else 0

        report = {
            'optimization_summary': {
                'total_triples': len(optimized_triples),
                'total_nodes': G.number_of_nodes(),
                'total_edges': G.number_of_edges(),
                'connectivity_ratio': connectivity_ratio,
                'avg_region_connections': avg_connections,
                'relation_gini': relation_gini,
                'num_relations': len(relation_counts)
            },
            'relation_distribution': dict(relation_counts),
            'region_connection_stats': {
                'min': min(region_conn_values) if region_conn_values else 0,
                'max': max(region_conn_values) if region_conn_values else 0,
                'mean': avg_connections,
                'std': np.std(region_conn_values) if region_conn_values else 0
            }
        }

        # 保存报告
        report_path = self.output_dir / "optimization_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📋 优化报告已保存到: {report_path}")

        # 打印关键指标
        print("\n🎯 优化后关键指标:")
        print(f"   - 连通性比例: {connectivity_ratio:.3f}")
        print(f"   - 区域平均连接度: {avg_connections:.1f}")
        print(f"   - 关系分布基尼系数: {relation_gini:.3f}")
        print(f"   - 关系类型数量: {len(relation_counts)}")

        return report

    def run_optimization(self):
        """运行完整的优化流程"""
        print("🚀 开始知识图谱优化流程...")
        print("=" * 60)

        # 1. 分析当前问题
        current_issues = self.analyze_current_issues()

        # 2. 生成优化知识图谱
        optimized_triples = self.generate_optimized_kg()

        # 3. 保存结果
        output_path = self.save_optimized_kg(optimized_triples)

        print("\n✅ 知识图谱优化完成！")
        print(f"📁 输出文件: {output_path}")
        print("\n💡 建议的后续步骤:")
        print("   1. 使用优化后的知识图谱重新训练RGCN模型")
        print("   2. 对比R²性能提升效果")
        print("   3. 根据结果进一步调整优化参数")

        return output_path


def main():
    """主函数 - 运行知识图谱优化"""

    # 配置路径（请根据实际情况修改）
    region_info_path = "./训练评估/data/shenyang/shenyang_region2allinfo.json"
    original_kg_path = "./训练评估/data/shenyang/kg_cuda_complete_21_relations_optimized.txt"

    # 创建优化器
    optimizer = KnowledgeGraphOptimizer(
        region_info_path=region_info_path,
        original_kg_path=original_kg_path,
        output_dir="./optimized_kg/"
    )

    # 运行优化
    output_path = optimizer.run_optimization()

    print(f"\n🎉 优化完成！新的知识图谱文件: {output_path}")


if __name__ == "__main__":
    main()
