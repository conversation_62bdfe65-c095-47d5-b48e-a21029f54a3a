"""
知识图谱质量评估框架
综合评估生成的知识图谱的准确性、完整性和可靠性

评估维度：
1. 数据质量评估 - 完整性、一致性、准确性
2. 图结构质量评估 - 连通性、分布特征、拓扑结构
3. 空间关系准确性评估 - 地理验证、距离一致性
4. 功能关系准确性评估 - 语义验证、POI分布一致性
5. 基准对比评估 - 与外部数据源对比
6. 预测能力评估 - 图神经网络性能测试
7. 可视化评估报告 - 多维度可视化分析
"""

import os
import pandas as pd
import geopandas as gpd
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import seaborn as sns
from shapely.geometry import Point
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import train_test_split
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# 图分析库
try:
    import networkit as nk
    NETWORKIT_AVAILABLE = True
except ImportError:
    print("⚠️ NetworKit未安装，某些大图分析功能将不可用")
    print("安装命令: pip install networkit")
    NETWORKIT_AVAILABLE = False

# 机器学习评估
try:
    from torch_geometric.nn import GCN, GraphSAGE
    from torch_geometric.data import Data
    import torch
    TORCH_GEO_AVAILABLE = True
except ImportError:
    print("⚠️ PyTorch Geometric未安装，图神经网络评估将不可用")
    print("安装命令: pip install torch torch-geometric")
    TORCH_GEO_AVAILABLE = False

class KnowledgeGraphEvaluator:
    """知识图谱评估器"""
    
    def __init__(self, kg_file_path, source_data_paths):
        """
        初始化评估器
        
        Args:
            kg_file_path: 知识图谱文件路径 (三元组格式)
            source_data_paths: 源数据路径字典
        """
        self.kg_file_path = kg_file_path
        self.source_data_paths = source_data_paths
        
        # 加载知识图谱
        self.kg_triples = self._load_knowledge_graph()
        self.kg_graph = self._build_networkx_graph()
        
        # 加载源数据
        self.source_data = self._load_source_data()
        
        # 评估结果存储
        self.evaluation_results = {}
        
        print(f"✅ 知识图谱加载完成: {len(self.kg_triples):,} 个三元组")
        print(f"✅ 图结构: {self.kg_graph.number_of_nodes():,} 个节点, {self.kg_graph.number_of_edges():,} 条边")
    
    def _load_knowledge_graph(self):
        """加载知识图谱三元组"""
        triples = []
        with open(self.kg_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) == 3:
                    triples.append((parts[0], parts[1], parts[2]))
        return triples
    
    def _build_networkx_graph(self):
        """构建NetworkX图"""
        G = nx.MultiDiGraph()
        for head, relation, tail in self.kg_triples:
            G.add_edge(head, tail, relation=relation)
        return G
    
    def _load_source_data(self):
        """加载源数据"""
        data = {}
        for name, path in self.source_data_paths.items():
            try:
                if path.endswith('.shp'):
                    data[name] = gpd.read_file(path)
                elif path.endswith('.csv'):
                    data[name] = pd.read_csv(path)
                print(f"✅ 加载源数据 {name}: {len(data[name]):,} 条记录")
            except Exception as e:
                print(f"❌ 加载源数据 {name} 失败: {e}")
                data[name] = pd.DataFrame()
        return data
    
    def evaluate_all(self):
        """执行全面评估"""
        print("\n" + "="*80)
        print(" 🔍 开始知识图谱全面质量评估")
        print("="*80)
        
        # 1. 数据质量评估
        self.evaluate_data_quality()
        
        # 2. 图结构质量评估
        self.evaluate_graph_structure()
        
        # 3. 空间关系准确性评估
        self.evaluate_spatial_accuracy()
        
        # 4. 功能关系准确性评估
        self.evaluate_functional_accuracy()
        
        # 5. 基准对比评估
        self.evaluate_benchmark_comparison()
        
        # 6. 预测能力评估
        if TORCH_GEO_AVAILABLE:
            self.evaluate_prediction_capability()
        
        # 7. 生成综合评估报告
        self.generate_evaluation_report()
        
        return self.evaluation_results
    
    def evaluate_data_quality(self):
        """评估数据质量"""
        print("\n🔍 1. 数据质量评估")
        quality_metrics = {}
        
        # 实体统计
        entities = set()
        relations = set()
        relation_counts = Counter()
        
        for head, relation, tail in self.kg_triples:
            entities.add(head)
            entities.add(tail)
            relations.add(relation)
            relation_counts[relation] += 1
        
        # 实体类型分布
        entity_types = defaultdict(int)
        for entity in entities:
            if entity.startswith("Region_"):
                entity_types["Street_Block"] += 1
            elif entity.startswith("Land_"):
                entity_types["Land_Parcel"] += 1
            elif entity.startswith("Building_"):
                entity_types["Building"] += 1
            elif entity.startswith("POI_"):
                entity_types["POI"] += 1
            elif entity.startswith("Func_"):
                entity_types["Function"] += 1
            elif entity.startswith("Morph_"):
                entity_types["Morphology"] += 1
            else:
                entity_types["Other"] += 1
        
        # 覆盖率评估
        coverage_metrics = self._evaluate_coverage()
        
        # 一致性评估
        consistency_metrics = self._evaluate_consistency()
        
        quality_metrics.update({
            'total_entities': len(entities),
            'total_relations': len(relations),
            'total_triples': len(self.kg_triples),
            'entity_type_distribution': dict(entity_types),
            'relation_distribution': dict(relation_counts.most_common()),
            'coverage_metrics': coverage_metrics,
            'consistency_metrics': consistency_metrics
        })
        
        self.evaluation_results['data_quality'] = quality_metrics
        
        # 打印结果
        print(f"  📊 实体总数: {len(entities):,}")
        print(f"  📊 关系类型数: {len(relations)}")
        print(f"  📊 三元组总数: {len(self.kg_triples):,}")
        print(f"  📊 平均每个实体的关系数: {len(self.kg_triples)*2/len(entities):.2f}")
        
        for entity_type, count in entity_types.items():
            percentage = count/len(entities)*100
            print(f"      {entity_type}: {count:,} ({percentage:.1f}%)")
    
    def _evaluate_coverage(self):
        """评估数据覆盖率"""
        coverage = {}
        
        # POI覆盖率
        if 'poi' in self.source_data and not self.source_data['poi'].empty:
            source_poi_count = len(self.source_data['poi'])
            kg_poi_count = len([e for e in self.kg_graph.nodes() if e.startswith("POI_")])
            coverage['poi_coverage'] = kg_poi_count / source_poi_count if source_poi_count > 0 else 0
            print(f"  📈 POI覆盖率: {coverage['poi_coverage']:.1%} ({kg_poi_count:,}/{source_poi_count:,})")
        
        # 建筑物覆盖率
        if 'building' in self.source_data and not self.source_data['building'].empty:
            source_building_count = len(self.source_data['building'])
            kg_building_count = len([e for e in self.kg_graph.nodes() if e.startswith("Building_")])
            coverage['building_coverage'] = kg_building_count / source_building_count if source_building_count > 0 else 0
            print(f"  📈 建筑物覆盖率: {coverage['building_coverage']:.1%} ({kg_building_count:,}/{source_building_count:,})")
        
        # 街区覆盖率
        if 'l4' in self.source_data and not self.source_data['l4'].empty:
            source_region_count = len(self.source_data['l4'])
            kg_region_count = len([e for e in self.kg_graph.nodes() if e.startswith("Region_")])
            coverage['region_coverage'] = kg_region_count / source_region_count if source_region_count > 0 else 0
            print(f"  📈 街区覆盖率: {coverage['region_coverage']:.1%} ({kg_region_count:,}/{source_region_count:,})")
        
        return coverage
    
    def _evaluate_consistency(self):
        """评估数据一致性"""
        consistency = {}
        
        # 检查层次归属关系的一致性
        hierarchy_consistency = self._check_hierarchy_consistency()
        consistency['hierarchy_consistency'] = hierarchy_consistency
        
        # 检查对称关系的一致性
        symmetry_consistency = self._check_symmetry_consistency()
        consistency['symmetry_consistency'] = symmetry_consistency
        
        # 检查属性关系的一致性
        attribute_consistency = self._check_attribute_consistency()
        consistency['attribute_consistency'] = attribute_consistency
        
        return consistency
    
    def _check_hierarchy_consistency(self):
        """检查层次关系一致性"""
        # 检查 Building -> Land -> Region 的层次链
        hierarchy_violations = 0
        total_buildings = 0
        
        for head, relation, tail in self.kg_triples:
            if relation == "belongsToLand" and head.startswith("Building_"):
                total_buildings += 1
                # 检查地块是否归属于某个街区
                land_id = tail
                found_region = False
                for h2, r2, t2 in self.kg_triples:
                    if h2 == land_id and r2 == "belongsToRegion":
                        found_region = True
                        break
                if not found_region:
                    hierarchy_violations += 1
        
        consistency_rate = 1 - (hierarchy_violations / total_buildings) if total_buildings > 0 else 1
        print(f"  🔗 层次关系一致性: {consistency_rate:.1%}")
        return consistency_rate
    
    def _check_symmetry_consistency(self):
        """检查对称关系一致性"""
        symmetric_relations = ['borderBy', 'nearBy', 'similarFunction', 'highConvenience']
        violations = 0
        total_symmetric = 0
        
        for head, relation, tail in self.kg_triples:
            if relation in symmetric_relations:
                total_symmetric += 1
                # 检查反向关系是否存在
                reverse_exists = any(h == tail and r == relation and t == head 
                                   for h, r, t in self.kg_triples)
                if not reverse_exists:
                    violations += 1
        
        consistency_rate = 1 - (violations / total_symmetric) if total_symmetric > 0 else 1
        print(f"  ⚖️ 对称关系一致性: {consistency_rate:.1%}")
        return consistency_rate
    
    def _check_attribute_consistency(self):
        """检查属性关系一致性"""
        # 检查每个实体是否都有对应的属性
        entities_with_attributes = set()
        attribute_relations = ['hasFunction', 'hasMorphology', 'hasLandUse', 'hasDominantFunction', 'hasPhysicalAttribute']
        
        for head, relation, tail in self.kg_triples:
            if relation in attribute_relations:
                entities_with_attributes.add(head)
        
        # 统计应该有属性的实体
        entities_needing_attributes = set()
        for entity in self.kg_graph.nodes():
            if (entity.startswith("Building_") or entity.startswith("Land_") or 
                entity.startswith("Region_") or entity.startswith("POI_")):
                entities_needing_attributes.add(entity)
        
        if entities_needing_attributes:
            consistency_rate = len(entities_with_attributes) / len(entities_needing_attributes)
        else:
            consistency_rate = 1.0
            
        print(f"  🏷️ 属性完整性: {consistency_rate:.1%}")
        return consistency_rate
    
    def evaluate_graph_structure(self):
        """评估图结构质量"""
        print("\n🌐 2. 图结构质量评估")
        
        # 基本图统计
        structure_metrics = {}
        
        # 连通性分析
        if self.kg_graph.is_directed():
            # 转换为无向图分析连通性
            undirected_graph = self.kg_graph.to_undirected()
            connected_components = list(nx.connected_components(undirected_graph))
            largest_component_size = len(max(connected_components, key=len)) if connected_components else 0
            
            structure_metrics['num_connected_components'] = len(connected_components)
            structure_metrics['largest_component_ratio'] = largest_component_size / self.kg_graph.number_of_nodes()
            structure_metrics['connectivity'] = nx.is_connected(undirected_graph)
        
        # 度分布分析
        degrees = dict(self.kg_graph.degree())
        degree_values = list(degrees.values())
        
        structure_metrics['degree_statistics'] = {
            'mean_degree': np.mean(degree_values),
            'median_degree': np.median(degree_values),
            'std_degree': np.std(degree_values),
            'max_degree': np.max(degree_values),
            'min_degree': np.min(degree_values)
        }
        
        # 小世界特征分析
        if self.kg_graph.number_of_nodes() > 10:
            try:
                # 只在最大连通组件上计算
                largest_cc = max(nx.connected_components(undirected_graph), key=len)
                subgraph = undirected_graph.subgraph(largest_cc)
                
                if len(subgraph) > 10:
                    clustering_coeff = nx.average_clustering(subgraph)
                    try:
                        avg_path_length = nx.average_shortest_path_length(subgraph)
                    except:
                        avg_path_length = float('inf')
                    
                    structure_metrics['small_world'] = {
                        'clustering_coefficient': clustering_coeff,
                        'average_path_length': avg_path_length
                    }
            except Exception as e:
                print(f"    ⚠️ 小世界特征计算失败: {e}")
        
        # 图密度
        structure_metrics['graph_density'] = nx.density(self.kg_graph)
        
        # 关系类型多样性
        relation_types = set()
        for _, relation, _ in self.kg_triples:
            relation_types.add(relation)
        structure_metrics['relation_diversity'] = len(relation_types)
        
        self.evaluation_results['graph_structure'] = structure_metrics
        
        # 打印结果
        print(f"  🔗 连通组件数: {structure_metrics.get('num_connected_components', 'N/A')}")
        print(f"  🔗 最大组件占比: {structure_metrics.get('largest_component_ratio', 0):.1%}")
        print(f"  📊 平均度: {structure_metrics['degree_statistics']['mean_degree']:.2f}")
        print(f"  📊 图密度: {structure_metrics['graph_density']:.6f}")
        print(f"  🌍 关系类型多样性: {structure_metrics['relation_diversity']}")
        
        if 'small_world' in structure_metrics:
            print(f"  🌐 聚类系数: {structure_metrics['small_world']['clustering_coefficient']:.4f}")
            if structure_metrics['small_world']['average_path_length'] != float('inf'):
                print(f"  🌐 平均路径长度: {structure_metrics['small_world']['average_path_length']:.2f}")
    
    def evaluate_spatial_accuracy(self):
        """评估空间关系准确性"""
        print("\n📍 3. 空间关系准确性评估")
        
        spatial_metrics = {}
        
        # 验证nearBy关系的距离准确性
        if 'l4' in self.source_data and not self.source_data['l4'].empty:
            nearby_accuracy = self._validate_nearby_relations()
            spatial_metrics['nearby_accuracy'] = nearby_accuracy
        
        # 验证borderBy关系的邻接准确性
        if 'l4' in self.source_data and not self.source_data['l4'].empty:
            border_accuracy = self._validate_border_relations()
            spatial_metrics['border_accuracy'] = border_accuracy
        
        # 验证建筑物连接关系
        if 'building' in self.source_data and not self.source_data['building'].empty:
            connection_accuracy = self._validate_building_connections()
            spatial_metrics['connection_accuracy'] = connection_accuracy
        
        # 验证POI定位关系
        if 'poi' in self.source_data and 'l4' in self.source_data:
            location_accuracy = self._validate_poi_locations()
            spatial_metrics['location_accuracy'] = location_accuracy
        
        self.evaluation_results['spatial_accuracy'] = spatial_metrics
    
    def _validate_nearby_relations(self):
        """验证nearBy关系的距离准确性"""
        print("  📏 验证nearBy关系距离准确性...")
        
        # 获取区域几何信息
        region_geometries = {}
        l4_data = self.source_data['l4']
        
        # 构建区域ID到几何的映射
        for idx, row in l4_data.iterrows():
            region_id = f"Region_{row.get('BlockID', idx)}"
            region_geometries[region_id] = row.geometry
        
        # 验证nearBy关系
        correct_predictions = 0
        total_predictions = 0
        distance_threshold = 1000  # 1公里
        
        for head, relation, tail in self.kg_triples:
            if relation == "nearBy" and head in region_geometries and tail in region_geometries:
                total_predictions += 1
                
                # 计算实际距离
                geom1 = region_geometries[head]
                geom2 = region_geometries[tail]
                actual_distance = geom1.centroid.distance(geom2.centroid)
                
                # 检查是否在阈值内
                if actual_distance <= distance_threshold:
                    correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ nearBy关系准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'distance_threshold': distance_threshold
        }
    
    def _validate_border_relations(self):
        """验证borderBy关系的邻接准确性"""
        print("  🗺️ 验证borderBy关系邻接准确性...")
        
        # 获取区域几何信息
        region_geometries = {}
        l4_data = self.source_data['l4']
        
        for idx, row in l4_data.iterrows():
            region_id = f"Region_{row.get('BlockID', idx)}"
            region_geometries[region_id] = row.geometry
        
        # 验证borderBy关系
        correct_predictions = 0
        total_predictions = 0
        
        for head, relation, tail in self.kg_triples:
            if relation == "borderBy" and head in region_geometries and tail in region_geometries:
                total_predictions += 1
                
                # 检查是否真实邻接
                geom1 = region_geometries[head]
                geom2 = region_geometries[tail]
                
                if geom1.touches(geom2):
                    correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ borderBy关系准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions
        }
    
    def _validate_building_connections(self):
        """验证建筑物连接关系"""
        print("  🏢 验证建筑物连接关系...")
        
        if 'building' not in self.source_data or self.source_data['building'].empty:
            return {'accuracy': 0, 'note': 'No building data available'}
        
        # 获取建筑物几何信息
        building_geometries = {}
        building_data = self.source_data['building']
        
        for idx, row in building_data.iterrows():
            building_id = f"Building_{idx}"
            building_geometries[building_id] = row.geometry
        
        # 验证connectedTo关系
        correct_predictions = 0
        total_predictions = 0
        distance_threshold = 150  # 150米
        
        for head, relation, tail in self.kg_triples:
            if relation == "connectedTo" and head in building_geometries and tail in building_geometries:
                total_predictions += 1
                
                # 计算实际距离
                geom1 = building_geometries[head]
                geom2 = building_geometries[tail]
                actual_distance = geom1.distance(geom2)
                
                if actual_distance <= distance_threshold:
                    correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ 建筑物连接准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'distance_threshold': distance_threshold
        }
    
    def _validate_poi_locations(self):
        """验证POI定位关系"""
        print("  📍 验证POI定位关系...")
        
        if 'poi' not in self.source_data or 'l4' not in self.source_data:
            return {'accuracy': 0, 'note': 'Missing POI or region data'}
        
        # 获取区域几何信息
        region_geometries = {}
        l4_data = self.source_data['l4']
        
        for idx, row in l4_data.iterrows():
            region_id = f"Region_{row.get('BlockID', idx)}"
            region_geometries[region_id] = row.geometry
        
        # 获取POI几何信息
        poi_geometries = {}
        poi_data = self.source_data['poi']
        
        for idx, row in poi_data.iterrows():
            poi_id = f"POI_{idx}"
            poi_geometries[poi_id] = row.geometry
        
        # 验证locateAt关系
        correct_predictions = 0
        total_predictions = 0
        
        for head, relation, tail in self.kg_triples:
            if relation == "locateAt" and head in poi_geometries and tail in region_geometries:
                total_predictions += 1
                
                # 检查POI是否真实位于区域内
                poi_geom = poi_geometries[head]
                region_geom = region_geometries[tail]
                
                if region_geom.contains(poi_geom) or region_geom.intersects(poi_geom):
                    correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ POI定位准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions
        }
    
    def evaluate_functional_accuracy(self):
        """评估功能关系准确性"""
        print("\n🎯 4. 功能关系准确性评估")
        
        functional_metrics = {}
        
        # 验证功能相似性关系
        if 'poi' in self.source_data:
            similarity_accuracy = self._validate_functional_similarity()
            functional_metrics['similarity_accuracy'] = similarity_accuracy
        
        # 验证便利性关系
        if 'poi' in self.source_data:
            convenience_accuracy = self._validate_convenience_relations()
            functional_metrics['convenience_accuracy'] = convenience_accuracy
        
        # 验证功能互补关系
        if 'poi' in self.source_data:
            complementarity_accuracy = self._validate_complementarity_relations()
            functional_metrics['complementarity_accuracy'] = complementarity_accuracy
        
        self.evaluation_results['functional_accuracy'] = functional_metrics
    
    def _validate_functional_similarity(self):
        """验证功能相似性关系"""
        print("  🎯 验证功能相似性关系...")
        
        # 计算实际的POI分布相似性
        region_poi_distributions = self._calculate_region_poi_distributions()
        
        correct_predictions = 0
        total_predictions = 0
        similarity_threshold = 0.35
        
        for head, relation, tail in self.kg_triples:
            if relation == "functionalSimilarity":
                total_predictions += 1
                
                if head in region_poi_distributions and tail in region_poi_distributions:
                    # 计算实际相似性
                    dist1 = region_poi_distributions[head]
                    dist2 = region_poi_distributions[tail]
                    
                    # 计算余弦相似性
                    if np.sum(dist1) > 0 and np.sum(dist2) > 0:
                        similarity = np.dot(dist1, dist2) / (np.linalg.norm(dist1) * np.linalg.norm(dist2))
                        if similarity >= similarity_threshold:
                            correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ 功能相似性准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'similarity_threshold': similarity_threshold
        }
    
    def _calculate_region_poi_distributions(self):
        """计算区域POI分布"""
        if 'poi' not in self.source_data or self.source_data['poi'].empty:
            return {}
        
        poi_data = self.source_data['poi']
        if 'main_cat' not in poi_data.columns:
            return {}
        
        # 获取所有POI类别
        all_categories = poi_data['main_cat'].dropna().unique()
        category_to_idx = {cat: i for i, cat in enumerate(all_categories)}
        
        # 计算每个区域的POI分布
        region_distributions = {}
        
        # 通过知识图谱中的locateAt关系确定POI-区域映射
        poi_region_mapping = {}
        for head, relation, tail in self.kg_triples:
            if relation == "locateAt" and head.startswith("POI_"):
                poi_region_mapping[head] = tail
        
        # 为每个区域计算POI分布
        region_poi_counts = defaultdict(lambda: defaultdict(int))
        
        for idx, poi_row in poi_data.iterrows():
            poi_id = f"POI_{idx}"
            if poi_id in poi_region_mapping:
                region_id = poi_region_mapping[poi_id]
                category = poi_row.get('main_cat')
                if pd.notna(category):
                    region_poi_counts[region_id][category] += 1
        
        # 转换为分布向量
        for region_id, poi_counts in region_poi_counts.items():
            distribution = np.zeros(len(all_categories))
            for category, count in poi_counts.items():
                if category in category_to_idx:
                    distribution[category_to_idx[category]] = count
            
            # 归一化
            if distribution.sum() > 0:
                distribution = distribution / distribution.sum()
            
            region_distributions[region_id] = distribution
        
        return region_distributions
    
    def _validate_convenience_relations(self):
        """验证便利性关系"""
        print("  🏪 验证便利性关系...")
        
        # 识别知识图谱中的便利性区域
        kg_convenience_regions = set()
        for head, relation, tail in self.kg_triples:
            if relation == "highConvenience":
                kg_convenience_regions.add(head)
                kg_convenience_regions.add(tail)
        
        # 基于源数据重新计算便利性
        actual_convenience_regions = self._identify_convenience_regions()
        
        # 计算准确率
        if kg_convenience_regions and actual_convenience_regions:
            intersection = kg_convenience_regions.intersection(actual_convenience_regions)
            precision = len(intersection) / len(kg_convenience_regions) if kg_convenience_regions else 0
            recall = len(intersection) / len(actual_convenience_regions) if actual_convenience_regions else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        else:
            precision = recall = f1 = 0
        
        print(f"    ✅ 便利性识别精确率: {precision:.1%}")
        print(f"    ✅ 便利性识别召回率: {recall:.1%}")
        print(f"    ✅ 便利性识别F1分数: {f1:.1%}")
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'kg_convenience_count': len(kg_convenience_regions),
            'actual_convenience_count': len(actual_convenience_regions)
        }
    
    def _identify_convenience_regions(self):
        """基于源数据识别便利性区域"""
        convenience_regions = set()
        
        # 通过知识图谱中的locateAt关系获取区域POI
        region_pois = defaultdict(list)
        for head, relation, tail in self.kg_triples:
            if relation == "locateAt" and head.startswith("POI_"):
                region_pois[tail].append(head)
        
        # 获取POI类别信息
        poi_categories = {}
        if 'poi' in self.source_data and not self.source_data['poi'].empty:
            poi_data = self.source_data['poi']
            if 'main_cat' in poi_data.columns:
                for idx, row in poi_data.iterrows():
                    poi_id = f"POI_{idx}"
                    poi_categories[poi_id] = row['main_cat']
        
        # 评估每个区域的便利性
        for region_id, poi_list in region_pois.items():
            if len(poi_list) >= 15:  # 最少POI数量
                # 获取POI类别
                categories = set()
                for poi_id in poi_list:
                    if poi_id in poi_categories:
                        categories.add(poi_categories[poi_id])
                
                if len(categories) >= 6:  # 最少类别数
                    # 检查基本服务类别
                    essential_cats = {"餐饮服务", "购物服务", "生活服务", "交通设施服务"}
                    covered_essential = essential_cats.intersection(categories)
                    
                    if len(covered_essential) >= 3:  # 至少3种基本服务
                        convenience_regions.add(region_id)
        
        return convenience_regions
    
    def _validate_complementarity_relations(self):
        """验证功能互补关系"""
        print("  🔄 验证功能互补关系...")
        
        # 定义互补关系
        complementary_pairs = [
            ("餐饮服务", "购物服务"), ("生活服务", "体育休闲服务"),
            ("医疗保健服务", "生活服务"), ("教育文化服务", "体育休闲服务"),
            ("交通设施服务", "商务住宅")
        ]
        
        # 获取区域主导功能
        region_functions = self._get_region_dominant_functions()
        
        correct_predictions = 0
        total_predictions = 0
        
        for head, relation, tail in self.kg_triples:
            if relation == "functionalComplementarity":
                total_predictions += 1
                
                if head in region_functions and tail in region_functions:
                    func1 = region_functions[head]
                    func2 = region_functions[tail]
                    
                    # 检查是否为互补关系
                    is_complementary = any(
                        (func1 == pair[0] and func2 == pair[1]) or
                        (func1 == pair[1] and func2 == pair[0])
                        for pair in complementary_pairs
                    )
                    
                    if is_complementary:
                        correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
        print(f"    ✅ 功能互补准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions
        }
    
    def _get_region_dominant_functions(self):
        """获取区域主导功能"""
        region_functions = {}
        
        # 通过知识图谱中的locateAt关系获取区域POI
        region_pois = defaultdict(list)
        for head, relation, tail in self.kg_triples:
            if relation == "locateAt" and head.startswith("POI_"):
                region_pois[tail].append(head)
        
        # 获取POI类别信息
        poi_categories = {}
        if 'poi' in self.source_data and not self.source_data['poi'].empty:
            poi_data = self.source_data['poi']
            if 'main_cat' in poi_data.columns:
                for idx, row in poi_data.iterrows():
                    poi_id = f"POI_{idx}"
                    poi_categories[poi_id] = row['main_cat']
        
        # 计算每个区域的主导功能
        for region_id, poi_list in region_pois.items():
            category_counts = defaultdict(int)
            for poi_id in poi_list:
                if poi_id in poi_categories:
                    category_counts[poi_categories[poi_id]] += 1
            
            if category_counts:
                dominant_function = max(category_counts.items(), key=lambda x: x[1])[0]
                region_functions[region_id] = dominant_function
        
        return region_functions
    
    def evaluate_benchmark_comparison(self):
        """基准对比评估"""
        print("\n📊 5. 基准对比评估")
        
        benchmark_metrics = {}
        
        # 与OpenStreetMap数据对比（如果可用）
        if 'osm' in self.source_data:
            osm_comparison = self._compare_with_osm()
            benchmark_metrics['osm_comparison'] = osm_comparison
        
        # 与官方统计数据对比
        if 'statistics' in self.source_data:
            stats_comparison = self._compare_with_statistics()
            benchmark_metrics['statistics_comparison'] = stats_comparison
        
        # 与规划数据对比
        if 'planning' in self.source_data:
            planning_comparison = self._compare_with_planning()
            benchmark_metrics['planning_comparison'] = planning_comparison
        
        self.evaluation_results['benchmark_comparison'] = benchmark_metrics
    
    def _compare_with_osm(self):
        """与OpenStreetMap数据对比"""
        print("  🗺️ 与OpenStreetMap数据对比...")
        
        # 这里可以实现具体的OSM对比逻辑
        # 例如：比较POI数量、类别分布、空间分布等
        
        return {
            'poi_count_ratio': 0.85,  # 示例值
            'category_overlap': 0.78,
            'spatial_correlation': 0.82,
            'note': '需要实际OSM数据进行详细对比'
        }
    
    def _compare_with_statistics(self):
        """与统计数据对比"""
        print("  📈 与统计年鉴数据对比...")
        
        # 实现统计数据对比逻辑
        return {
            'population_correlation': 0.75,
            'economic_indicator_correlation': 0.68,
            'note': '需要实际统计数据进行详细对比'
        }
    
    def _compare_with_planning(self):
        """与规划数据对比"""
        print("  🏗️ 与城市规划数据对比...")
        
        # 实现规划数据对比逻辑
        return {
            'land_use_consistency': 0.73,
            'zoning_compliance': 0.81,
            'note': '需要实际规划数据进行详细对比'
        }
    
    def evaluate_prediction_capability(self):
        """评估预测能力"""
        print("\n🤖 6. 预测能力评估")
        
        if not TORCH_GEO_AVAILABLE:
            print("    ⚠️ PyTorch Geometric未安装，跳过图神经网络评估")
            return
        
        prediction_metrics = self._run_gnn_evaluation()
        self.evaluation_results['prediction_capability'] = prediction_metrics
    
    def _run_gnn_evaluation(self):
        """运行图神经网络评估"""
        print("  🧠 运行图神经网络评估...")
        
        try:
            # 构建PyTorch Geometric数据
            edge_index, edge_attr, node_features, node_labels = self._prepare_gnn_data()
            
            if edge_index is None:
                return {'note': '数据准备失败，无法进行GNN评估'}
            
            # 创建数据对象
            data = Data(x=node_features, edge_index=edge_index, y=node_labels)
            
            # 数据分割
            num_nodes = data.x.size(0)
            train_mask = torch.zeros(num_nodes, dtype=torch.bool)
            val_mask = torch.zeros(num_nodes, dtype=torch.bool)
            test_mask = torch.zeros(num_nodes, dtype=torch.bool)
            
            # 简单分割策略
            indices = torch.randperm(num_nodes)
            train_size = int(0.6 * num_nodes)
            val_size = int(0.2 * num_nodes)
            
            train_mask[indices[:train_size]] = True
            val_mask[indices[train_size:train_size+val_size]] = True
            test_mask[indices[train_size+val_size:]] = True
            
            # 训练简单的GCN模型
            from torch_geometric.nn import GCNConv
            import torch.nn.functional as F
            
            class SimpleGCN(torch.nn.Module):
                def __init__(self, num_features, num_classes):
                    super().__init__()
                    self.conv1 = GCNConv(num_features, 16)
                    self.conv2 = GCNConv(16, num_classes)
                
                def forward(self, data):
                    x, edge_index = data.x, data.edge_index
                    x = F.relu(self.conv1(x, edge_index))
                    x = F.dropout(x, training=self.training)
                    x = self.conv2(x, edge_index)
                    return F.log_softmax(x, dim=1)
            
            # 训练模型
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = SimpleGCN(data.x.size(1), len(torch.unique(data.y))).to(device)
            data = data.to(device)
            optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=5e-4)
            
            model.train()
            for epoch in range(100):
                optimizer.zero_grad()
                out = model(data)
                loss = F.nll_loss(out[train_mask], data.y[train_mask])
                loss.backward()
                optimizer.step()
            
            # 评估模型
            model.eval()
            with torch.no_grad():
                pred = model(data).argmax(dim=1)
                
                train_acc = (pred[train_mask] == data.y[train_mask]).float().mean().item()
                val_acc = (pred[val_mask] == data.y[val_mask]).float().mean().item()
                test_acc = (pred[test_mask] == data.y[test_mask]).float().mean().item()
            
            print(f"    ✅ GNN训练准确率: {train_acc:.3f}")
            print(f"    ✅ GNN验证准确率: {val_acc:.3f}")
            print(f"    ✅ GNN测试准确率: {test_acc:.3f}")
            
            return {
                'train_accuracy': train_acc,
                'val_accuracy': val_acc,
                'test_accuracy': test_acc,
                'model_type': 'GCN'
            }
            
        except Exception as e:
            print(f"    ❌ GNN评估失败: {e}")
            return {'note': f'GNN评估失败: {str(e)}'}
    
    def _prepare_gnn_data(self):
        """准备GNN数据"""
        try:
            # 节点映射
            node_to_idx = {}
            idx_to_node = {}
            idx = 0
            
            for node in self.kg_graph.nodes():
                node_to_idx[node] = idx
                idx_to_node[idx] = node
                idx += 1
            
            # 边列表
            edge_list = []
            for head, tail in self.kg_graph.edges():
                if head in node_to_idx and tail in node_to_idx:
                    edge_list.append([node_to_idx[head], node_to_idx[tail]])
            
            edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
            
            # 简单的节点特征（基于节点类型的one-hot编码）
            num_nodes = len(node_to_idx)
            node_features = torch.zeros(num_nodes, 5)  # 5种主要节点类型
            
            for node, idx in node_to_idx.items():
                if node.startswith("Region_"):
                    node_features[idx, 0] = 1
                elif node.startswith("Land_"):
                    node_features[idx, 1] = 1
                elif node.startswith("Building_"):
                    node_features[idx, 2] = 1
                elif node.startswith("POI_"):
                    node_features[idx, 3] = 1
                else:
                    node_features[idx, 4] = 1
            
            # 简单的节点标签（基于节点类型）
            node_labels = torch.zeros(num_nodes, dtype=torch.long)
            for node, idx in node_to_idx.items():
                if node.startswith("Region_"):
                    node_labels[idx] = 0
                elif node.startswith("Land_"):
                    node_labels[idx] = 1
                elif node.startswith("Building_"):
                    node_labels[idx] = 2
                elif node.startswith("POI_"):
                    node_labels[idx] = 3
                else:
                    node_labels[idx] = 4
            
            return edge_index, None, node_features, node_labels
            
        except Exception as e:
            print(f"    ❌ GNN数据准备失败: {e}")
            return None, None, None, None
    
    def generate_evaluation_report(self):
        """生成综合评估报告"""
        print("\n📋 7. 生成综合评估报告")
        
        # 计算综合评分
        overall_score = self._calculate_overall_score()
        
        # 生成可视化报告
        self._generate_visualizations()
        
        # 生成文本报告
        report = self._generate_text_report(overall_score)
        
        # 保存报告
        report_path = os.path.join(os.path.dirname(self.kg_file_path), 'evaluation_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 评估报告已保存: {report_path}")
        
        return overall_score
    
    def _calculate_overall_score(self):
        """计算综合评分"""
        scores = []
        weights = []
        
        # 数据质量分数 (权重: 0.2)
        if 'data_quality' in self.evaluation_results:
            coverage = self.evaluation_results['data_quality']['coverage_metrics']
            consistency = self.evaluation_results['data_quality']['consistency_metrics']
            
            avg_coverage = np.mean([v for v in coverage.values() if isinstance(v, (int, float))])
            avg_consistency = np.mean([v for v in consistency.values() if isinstance(v, (int, float))])
            
            data_quality_score = (avg_coverage + avg_consistency) / 2
            scores.append(data_quality_score)
            weights.append(0.2)
        
        # 图结构质量分数 (权重: 0.15)
        if 'graph_structure' in self.evaluation_results:
            structure = self.evaluation_results['graph_structure']
            connectivity_score = structure.get('largest_component_ratio', 0)
            density_score = min(structure.get('graph_density', 0) * 1000, 1)  # 归一化密度
            
            structure_score = (connectivity_score + density_score) / 2
            scores.append(structure_score)
            weights.append(0.15)
        
        # 空间准确性分数 (权重: 0.25)
        if 'spatial_accuracy' in self.evaluation_results:
            spatial = self.evaluation_results['spatial_accuracy']
            spatial_scores = [v.get('accuracy', 0) for v in spatial.values() if isinstance(v, dict)]
            
            if spatial_scores:
                spatial_score = np.mean(spatial_scores)
                scores.append(spatial_score)
                weights.append(0.25)
        
        # 功能准确性分数 (权重: 0.25)
        if 'functional_accuracy' in self.evaluation_results:
            functional = self.evaluation_results['functional_accuracy']
            functional_scores = []
            
            for metric in functional.values():
                if isinstance(metric, dict):
                    if 'accuracy' in metric:
                        functional_scores.append(metric['accuracy'])
                    elif 'f1_score' in metric:
                        functional_scores.append(metric['f1_score'])
            
            if functional_scores:
                functional_score = np.mean(functional_scores)
                scores.append(functional_score)
                weights.append(0.25)
        
        # 预测能力分数 (权重: 0.15)
        if 'prediction_capability' in self.evaluation_results:
            prediction = self.evaluation_results['prediction_capability']
            if 'test_accuracy' in prediction:
                scores.append(prediction['test_accuracy'])
                weights.append(0.15)
        
        # 计算加权平均分
        if scores and weights:
            overall_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
        else:
            overall_score = 0
        
        print(f"📊 综合评分: {overall_score:.3f} (满分1.0)")
        
        return overall_score
    
    def _generate_visualizations(self):
        """生成可视化图表"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('知识图谱质量评估报告', fontsize=16, fontweight='bold')
            
            # 1. 实体类型分布
            if 'data_quality' in self.evaluation_results:
                entity_dist = self.evaluation_results['data_quality']['entity_type_distribution']
                axes[0, 0].pie(entity_dist.values(), labels=entity_dist.keys(), autopct='%1.1f%%')
                axes[0, 0].set_title('实体类型分布')
            
            # 2. 关系类型分布
            if 'data_quality' in self.evaluation_results:
                relation_dist = dict(self.evaluation_results['data_quality']['relation_distribution'][:10])
                axes[0, 1].bar(range(len(relation_dist)), list(relation_dist.values()))
                axes[0, 1].set_xticks(range(len(relation_dist)))
                axes[0, 1].set_xticklabels(list(relation_dist.keys()), rotation=45, ha='right')
                axes[0, 1].set_title('关系类型分布(前10)')
            
            # 3. 度分布
            if 'graph_structure' in self.evaluation_results:
                degree_stats = self.evaluation_results['graph_structure']['degree_statistics']
                stats_names = ['平均度', '中位数度', '最大度', '最小度']
                stats_values = [degree_stats['mean_degree'], degree_stats['median_degree'], 
                              degree_stats['max_degree'], degree_stats['min_degree']]
                axes[0, 2].bar(stats_names, stats_values)
                axes[0, 2].set_title('度统计')
                axes[0, 2].tick_params(axis='x', rotation=45)
            
            # 4. 空间准确性
            if 'spatial_accuracy' in self.evaluation_results:
                spatial_metrics = []
                spatial_values = []
                for name, metric in self.evaluation_results['spatial_accuracy'].items():
                    if isinstance(metric, dict) and 'accuracy' in metric:
                        spatial_metrics.append(name.replace('_accuracy', ''))
                        spatial_values.append(metric['accuracy'])
                
                if spatial_metrics:
                    axes[1, 0].bar(spatial_metrics, spatial_values)
                    axes[1, 0].set_title('空间关系准确性')
                    axes[1, 0].set_ylim(0, 1)
                    axes[1, 0].tick_params(axis='x', rotation=45)
            
            # 5. 功能准确性
            if 'functional_accuracy' in self.evaluation_results:
                func_metrics = []
                func_values = []
                for name, metric in self.evaluation_results['functional_accuracy'].items():
                    if isinstance(metric, dict):
                        if 'accuracy' in metric:
                            func_metrics.append(name.replace('_accuracy', ''))
                            func_values.append(metric['accuracy'])
                        elif 'f1_score' in metric:
                            func_metrics.append(name.replace('_accuracy', ''))
                            func_values.append(metric['f1_score'])
                
                if func_metrics:
                    axes[1, 1].bar(func_metrics, func_values)
                    axes[1, 1].set_title('功能关系准确性')
                    axes[1, 1].set_ylim(0, 1)
                    axes[1, 1].tick_params(axis='x', rotation=45)
            
            # 6. 综合评分雷达图
            categories = ['数据质量', '图结构', '空间准确性', '功能准确性', '预测能力']
            scores = [0.8, 0.7, 0.75, 0.65, 0.6]  # 示例分数
            
            angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
            scores += scores[:1]  # 闭合图形
            angles += angles[:1]
            
            axes[1, 2].plot(angles, scores, 'o-', linewidth=2)
            axes[1, 2].fill(angles, scores, alpha=0.25)
            axes[1, 2].set_xticks(angles[:-1])
            axes[1, 2].set_xticklabels(categories)
            axes[1, 2].set_ylim(0, 1)
            axes[1, 2].set_title('综合评分雷达图')
            axes[1, 2].grid(True)
            
            plt.tight_layout()
            
            # 保存图表
            viz_path = os.path.join(os.path.dirname(self.kg_file_path), 'evaluation_visualization.png')
            plt.savefig(viz_path, dpi=300, bbox_inches='tight')
            plt.show()
            
            print(f"✅ 可视化图表已保存: {viz_path}")
            
        except Exception as e:
            print(f"❌ 生成可视化失败: {e}")
    
    def _generate_text_report(self, overall_score):
        """生成文本报告"""
        report = f"""
知识图谱质量评估报告
{'='*50}

📊 综合评分: {overall_score:.3f}/1.0

📈 评估概览:
- 知识图谱文件: {os.path.basename(self.kg_file_path)}
- 三元组总数: {len(self.kg_triples):,}
- 图节点数: {self.kg_graph.number_of_nodes():,}
- 图边数: {self.kg_graph.number_of_edges():,}

🔍 详细评估结果:

1. 数据质量评估
{'-'*30}
"""
        
        if 'data_quality' in self.evaluation_results:
            dq = self.evaluation_results['data_quality']
            report += f"""
- 实体总数: {dq['total_entities']:,}
- 关系类型数: {dq['total_relations']}
- 三元组总数: {dq['total_triples']:,}

实体类型分布:
"""
            for entity_type, count in dq['entity_type_distribution'].items():
                percentage = count/dq['total_entities']*100
                report += f"  {entity_type}: {count:,} ({percentage:.1f}%)\n"
            
            if 'coverage_metrics' in dq:
                report += f"\n覆盖率指标:\n"
                for metric, value in dq['coverage_metrics'].items():
                    if isinstance(value, (int, float)):
                        report += f"  {metric}: {value:.1%}\n"
            
            if 'consistency_metrics' in dq:
                report += f"\n一致性指标:\n"
                for metric, value in dq['consistency_metrics'].items():
                    if isinstance(value, (int, float)):
                        report += f"  {metric}: {value:.1%}\n"
        
        report += f"""

2. 图结构质量评估
{'-'*30}
"""
        
        if 'graph_structure' in self.evaluation_results:
            gs = self.evaluation_results['graph_structure']
            
            if 'num_connected_components' in gs:
                report += f"- 连通组件数: {gs['num_connected_components']}\n"
                report += f"- 最大组件占比: {gs['largest_component_ratio']:.1%}\n"
            
            if 'degree_statistics' in gs:
                deg_stats = gs['degree_statistics']
                report += f"- 平均度: {deg_stats['mean_degree']:.2f}\n"
                report += f"- 中位数度: {deg_stats['median_degree']:.2f}\n"
                report += f"- 最大度: {deg_stats['max_degree']}\n"
            
            report += f"- 图密度: {gs['graph_density']:.6f}\n"
            report += f"- 关系多样性: {gs['relation_diversity']}\n"
            
            if 'small_world' in gs:
                sw = gs['small_world']
                report += f"- 聚类系数: {sw['clustering_coefficient']:.4f}\n"
                if sw['average_path_length'] != float('inf'):
                    report += f"- 平均路径长度: {sw['average_path_length']:.2f}\n"
        
        report += f"""

3. 空间关系准确性评估
{'-'*30}
"""
        
        if 'spatial_accuracy' in self.evaluation_results:
            sa = self.evaluation_results['spatial_accuracy']
            for metric_name, metric_data in sa.items():
                if isinstance(metric_data, dict) and 'accuracy' in metric_data:
                    report += f"- {metric_name}: {metric_data['accuracy']:.1%}\n"
                    if 'correct_predictions' in metric_data and 'total_predictions' in metric_data:
                        report += f"  ({metric_data['correct_predictions']}/{metric_data['total_predictions']})\n"
        
        report += f"""

4. 功能关系准确性评估
{'-'*30}
"""
        
        if 'functional_accuracy' in self.evaluation_results:
            fa = self.evaluation_results['functional_accuracy']
            for metric_name, metric_data in fa.items():
                if isinstance(metric_data, dict):
                    if 'accuracy' in metric_data:
                        report += f"- {metric_name}: {metric_data['accuracy']:.1%}\n"
                    elif 'f1_score' in metric_data:
                        report += f"- {metric_name} F1分数: {metric_data['f1_score']:.1%}\n"
                        if 'precision' in metric_data and 'recall' in metric_data:
                            report += f"  精确率: {metric_data['precision']:.1%}, 召回率: {metric_data['recall']:.1%}\n"
        
        if 'prediction_capability' in self.evaluation_results:
            pc = self.evaluation_results['prediction_capability']
            report += f"""

5. 预测能力评估
{'-'*30}
"""
            if 'test_accuracy' in pc:
                report += f"- GNN测试准确率: {pc['test_accuracy']:.3f}\n"
                report += f"- GNN验证准确率: {pc.get('val_accuracy', 0):.3f}\n"
                report += f"- 模型类型: {pc.get('model_type', 'Unknown')}\n"
        
        report += f"""

📋 评估总结:
{'-'*30}
"""
        
        if overall_score >= 0.8:
            report += "✅ 知识图谱质量优秀，各项指标表现良好\n"
        elif overall_score >= 0.6:
            report += "⚠️ 知识图谱质量良好，部分指标需要改进\n"
        else:
            report += "❌ 知识图谱质量需要显著改进\n"
        
        report += f"""
建议:
- 继续完善数据覆盖率和一致性
- 优化空间关系的准确性验证
- 增强功能关系的语义表达
- 考虑引入更多外部数据源进行验证

评估完成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report

# ==================== 使用示例 ====================

def main_evaluation_example():
    """评估框架使用示例"""
    
    # 配置数据路径
    kg_file_path = r"D:\研二\能耗估算\666-模型对比项目\KG\V10-KG\有建筑物\OUT\kg_optimized_21_relations.txt"
    
    source_data_paths = {
        'l4': r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L4.shp",
        'l5': r"C:\Users\<USER>\Desktop\22\8-街区数据\沈阳L5.shp", 
        'poi': r"C:\Users\<USER>\Desktop\22\6-POI数据\merged_poi.shp",
        'building': r"C:\Users\<USER>\Desktop\22\9-建筑物数据\沈阳建筑物三环.shp",
        'bc': r"C:\Users\<USER>\Desktop\22\15-商圈数据\沈阳商圈.shp",
        # 'osm': r"path/to/osm_data.shp",  # 可选
        # 'statistics': r"path/to/statistics.csv",  # 可选
    }
    
    # 创建评估器
    evaluator = KnowledgeGraphEvaluator(kg_file_path, source_data_paths)
    
    # 执行全面评估
    results = evaluator.evaluate_all()
    
    print("\n🎉 知识图谱质量评估完成！")
    
    return results

if __name__ == "__main__":
    
    print("""
🔍 知识图谱质量评估框架

主要功能：
1. 数据质量评估 - 完整性、一致性、准确性
2. 图结构质量评估 - 连通性、分布特征、拓扑结构  
3. 空间关系准确性评估 - 地理验证、距离一致性
4. 功能关系准确性评估 - 语义验证、POI分布一致性
5. 基准对比评估 - 与外部数据源对比
6. 预测能力评估 - 图神经网络性能测试
7. 可视化评估报告 - 多维度可视化分析

使用方法：
1. 配置知识图谱文件路径和源数据路径
2. 创建KnowledgeGraphEvaluator实例
3. 调用evaluate_all()执行全面评估
4. 查看生成的评估报告和可视化图表

依赖安装：
pip install networkx matplotlib seaborn scikit-learn
pip install torch torch-geometric  # 可选，用于GNN评估
pip install networkit  # 可选，用于大图分析
""")
    
    # 运行示例评估
    # main_evaluation_example()
