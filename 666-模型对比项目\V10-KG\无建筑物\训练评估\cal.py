import torch
import dgl
import numpy as np


def ccorr(a, b):
    """
    循环相关运算 (Circular Correlation)
    用于ComplEx等知识图谱嵌入方法
    
    Args:
        a, b: 输入张量
    
    Returns:
        循环相关结果
    """
    return torch.fft.irfft(torch.conj(torch.fft.rfft(a)) * torch.fft.rfft(b), a.shape[-1])


def rotate(h, r):
    """
    旋转操作，用于RotatE等模型
    
    Args:
        h: 头实体嵌入
        r: 关系嵌入
    
    Returns:
        旋转后的嵌入
    """
    # 将复数表示分为实部和虚部
    re_h, im_h = torch.chunk(h, 2, dim=-1)
    re_r, im_r = torch.chunk(r, 2, dim=-1)
    
    # 复数乘法
    re_hr = re_h * re_r - im_h * im_r
    im_hr = re_h * im_r + im_h * re_r
    
    return torch.cat([re_hr, im_hr], dim=-1)


def in_out_norm(g):
    """
    对图进行入度和出度归一化
    
    Args:
        g: DGL图对象
    
    Returns:
        归一化后的图对象
    """
    print("对图进行入度出度归一化...")
    
    # 计算入度和出度
    in_deg = g.in_degrees(range(g.num_nodes())).float()
    out_deg = g.out_degrees(range(g.num_nodes())).float()
    
    # 避免除零错误
    in_deg[in_deg == 0] = 1
    out_deg[out_deg == 0] = 1
    
    # 计算归一化系数
    norm_in = 1.0 / in_deg
    norm_out = 1.0 / out_deg
    
    # 将归一化系数存储在图的边数据中
    g.ndata['norm_in'] = norm_in.unsqueeze(1)
    g.ndata['norm_out'] = norm_out.unsqueeze(1)
    
    # 为边添加归一化权重
    src, dst = g.edges()
    edge_norm = torch.sqrt(norm_out[src] * norm_in[dst])
    g.edata['norm'] = edge_norm.unsqueeze(1)
    
    print(f"图归一化完成: {g.num_nodes()}个节点, {g.num_edges()}条边")
    return g


def add_self_loops(g):
    """
    为图添加自环
    
    Args:
        g: DGL图对象
    
    Returns:
        添加自环后的图对象
    """
    print("为图添加自环...")
    
    # 检查是否已有自环
    self_loop_edges = []
    src, dst = g.edges()
    
    existing_self_loops = set()
    for i in range(len(src)):
        if src[i].item() == dst[i].item():
            existing_self_loops.add(src[i].item())
    
    # 添加缺失的自环
    nodes_to_add = []
    for node_id in range(g.num_nodes()):
        if node_id not in existing_self_loops:
            nodes_to_add.append(node_id)
    
    if nodes_to_add:
        g = dgl.add_self_loop(g)
        print(f"添加了 {len(nodes_to_add)} 个自环")
    else:
        print("所有节点都已有自环")
    
    return g


def compute_graph_statistics(g):
    """
    计算图的统计信息
    
    Args:
        g: DGL图对象
    
    Returns:
        包含统计信息的字典
    """
    stats = {}
    
    # 基本统计
    stats['num_nodes'] = g.num_nodes()
    stats['num_edges'] = g.num_edges()
    
    # 度数统计
    in_degrees = g.in_degrees().float()
    out_degrees = g.out_degrees().float()
    
    stats['avg_in_degree'] = torch.mean(in_degrees).item()
    stats['avg_out_degree'] = torch.mean(out_degrees).item()
    stats['max_in_degree'] = torch.max(in_degrees).item()
    stats['max_out_degree'] = torch.max(out_degrees).item()
    
    # 连通性统计（简单版本）
    stats['density'] = g.num_edges() / (g.num_nodes() ** 2)
    
    return stats


def normalize_features(features, method='l2'):
    """
    特征归一化
    
    Args:
        features: 特征张量
        method: 归一化方法 ('l2', 'minmax', 'zscore')
    
    Returns:
        归一化后的特征
    """
    if method == 'l2':
        # L2归一化
        norm = torch.norm(features, p=2, dim=1, keepdim=True)
        norm[norm == 0] = 1  # 避免除零
        normalized = features / norm
        
    elif method == 'minmax':
        # 最小-最大归一化
        min_vals = torch.min(features, dim=0, keepdim=True)[0]
        max_vals = torch.max(features, dim=0, keepdim=True)[0]
        range_vals = max_vals - min_vals
        range_vals[range_vals == 0] = 1  # 避免除零
        normalized = (features - min_vals) / range_vals
        
    elif method == 'zscore':
        # Z-score标准化
        mean = torch.mean(features, dim=0, keepdim=True)
        std = torch.std(features, dim=0, keepdim=True)
        std[std == 0] = 1  # 避免除零
        normalized = (features - mean) / std
        
    else:
        raise ValueError(f"不支持的归一化方法: {method}")
    
    return normalized


def graph_sampling(g, sample_nodes=None, sample_ratio=0.1):
    """
    图采样，用于大图的子图采样
    
    Args:
        g: 原始图
        sample_nodes: 指定采样的节点列表
        sample_ratio: 采样比例
    
    Returns:
        采样后的子图
    """
    if sample_nodes is None:
        # 随机采样节点
        num_sample = int(g.num_nodes() * sample_ratio)
        sample_nodes = torch.randperm(g.num_nodes())[:num_sample]
    
    # 创建子图
    subgraph = dgl.node_subgraph(g, sample_nodes)
    
    print(f"图采样完成: {g.num_nodes()} -> {subgraph.num_nodes()} 节点")
    return subgraph, sample_nodes


if __name__ == "__main__":
    # 测试功能
    print("测试计算工具模块...")
    
    # 创建测试图
    src = torch.tensor([0, 1, 2, 0, 2])
    dst = torch.tensor([1, 2, 0, 2, 1])
    g = dgl.graph((src, dst))
    
    print(f"原始图: {g.num_nodes()}节点, {g.num_edges()}边")
    
    # 测试归一化
    g_norm = in_out_norm(g)
    
    # 测试统计信息
    stats = compute_graph_statistics(g_norm)
    print("图统计信息:", stats)
    
    # 测试特征归一化
    features = torch.randn(g.num_nodes(), 10)
    norm_features = normalize_features(features, 'l2')
    print(f"特征归一化: {features.shape} -> {norm_features.shape}")