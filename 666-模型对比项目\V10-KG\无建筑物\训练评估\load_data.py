import os
import dgl
import torch
import numpy as np
import pandas as pd
from collections import defaultdict


class Data_CompGCN:
    """知识图谱数据加载和处理类 - 灵活的图构建"""
    
    def __init__(self, kg_path, dataset_name):
        self.kg_path = kg_path
        self.dataset_name = dataset_name
        
        # 初始化映射字典
        self.ent2id = {}
        self.id2ent = {}
        self.rel2id = {}
        self.id2rel = {}
        
        # 加载知识图谱
        self.load_kg()
        self.build_graph()
        
        print(f"知识图谱加载完成:")
        print(f"  实体数量: {len(self.ent2id)}")
        print(f"  关系数量: {len(self.rel2id)}")
        print(f"  三元组数量: {len(self.triples)}")
        print(f"  原始图: {self.g.num_nodes()}个节点, {self.g.num_edges()}条边")
    
    def load_kg(self):
        """加载知识图谱文件"""
        if not os.path.exists(self.kg_path):
            print(f"警告: 知识图谱文件不存在 {self.kg_path}")
            # 创建示例数据
            self.create_sample_kg()
            return
        
        print(f"加载知识图谱: {self.kg_path}")
        
        self.triples = []
        entities = set()
        relations = set()
        
        try:
            with open(self.kg_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析三元组 (头实体, 关系, 尾实体)
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        head, relation, tail = parts[0], parts[1], parts[2]
                        entities.add(head)
                        entities.add(tail)
                        relations.add(relation)
                        self.triples.append((head, relation, tail))
                    else:
                        print(f"警告: 第{line_num+1}行格式不正确: {line}")
        
        except Exception as e:
            print(f"错误: 读取知识图谱文件失败 - {e}")
            self.create_sample_kg()
            return
        
        # 创建映射
        self.ent2id = {ent: idx for idx, ent in enumerate(sorted(entities))}
        self.id2ent = {idx: ent for ent, idx in self.ent2id.items()}
        self.rel2id = {rel: idx for idx, rel in enumerate(sorted(relations))}
        self.id2rel = {idx: rel for rel, idx in self.rel2id.items()}
    
    def create_sample_kg(self):
        """创建示例知识图谱数据"""
        print("创建示例知识图谱数据...")
        
        # 创建示例实体和关系
        regions = [f"Region_{i}" for i in range(50)]
        pois = [f"POI_{i}" for i in range(100)]
        entities = regions + pois + ["City_Shenyang", "District_Heping", "District_Shenhe"]
        
        relations = ["located_in", "adjacent_to", "contains", "near_to", "part_of"]
        
        # 创建映射
        self.ent2id = {ent: idx for idx, ent in enumerate(entities)}
        self.id2ent = {idx: ent for ent, idx in self.ent2id.items()}
        self.rel2id = {rel: idx for idx, rel in enumerate(relations)}
        self.id2rel = {idx: rel for rel, idx in self.rel2id.items()}
        
        # 生成示例三元组
        self.triples = []
        np.random.seed(42)
        
        # 区域之间的邻接关系
        for i in range(len(regions)-1):
            if np.random.random() > 0.7:
                self.triples.append((regions[i], "adjacent_to", regions[i+1]))
        
        # POI与区域的关系
        for poi in pois:
            region = np.random.choice(regions)
            self.triples.append((poi, "located_in", region))
        
        # 区域与行政区的关系
        for region in regions[:25]:
            self.triples.append((region, "part_of", "District_Heping"))
        for region in regions[25:]:
            self.triples.append((region, "part_of", "District_Shenhe"))
        
        # 行政区与城市的关系
        self.triples.append(("District_Heping", "part_of", "City_Shenyang"))
        self.triples.append(("District_Shenhe", "part_of", "City_Shenyang"))
    
    def build_graph(self):
        """构建基础DGL图 - 仅包含原始三元组"""
        print("构建基础DGL图（仅原始三元组）...")
        
        num_nodes = len(self.ent2id)
        
        # 收集原始边
        src_ids = []
        dst_ids = []
        edge_types = []
        
        for head, relation, tail in self.triples:
            head_id = self.ent2id[head]
            tail_id = self.ent2id[tail]
            rel_id = self.rel2id[relation]
            
            src_ids.append(head_id)
            dst_ids.append(tail_id)
            edge_types.append(rel_id)
        
        # 创建基础DGL图
        if src_ids:  # 确保有边存在
            self.g = dgl.graph((src_ids, dst_ids), num_nodes=num_nodes)
            self.g.edata['etype'] = torch.tensor(edge_types, dtype=torch.long)
        else:
            # 如果没有边，创建空图
            self.g = dgl.graph(([], []), num_nodes=num_nodes)
            self.g.edata['etype'] = torch.tensor([], dtype=torch.long)
        
        # 添加节点特征（初始化为随机特征）
        self.g.ndata['feat'] = torch.randn(num_nodes, 64)  # 64维随机特征
        
        print(f"基础DGL图构建完成: {self.g.num_nodes()}个节点, {self.g.num_edges()}条边")
    
    def add_reverse_edges(self):
        """为模型添加反向边（如果需要）"""
        print("添加反向边...")
        
        # 获取原始边信息
        src, dst = self.g.edges()
        edge_types = self.g.edata['etype']
        
        # 创建反向边
        reverse_src = dst
        reverse_dst = src
        # 反向关系ID = 原关系ID + 原关系总数
        reverse_edge_types = edge_types + len(self.rel2id)
        
        # 合并原始边和反向边
        all_src = torch.cat([src, reverse_src])
        all_dst = torch.cat([dst, reverse_dst])
        all_edge_types = torch.cat([edge_types, reverse_edge_types])
        
        # 重新创建图
        self.g = dgl.graph((all_src, all_dst), num_nodes=self.g.num_nodes())
        self.g.edata['etype'] = all_edge_types
        self.g.ndata['feat'] = torch.randn(self.g.num_nodes(), 64)
        
        print(f"添加反向边后: {self.g.num_nodes()}个节点, {self.g.num_edges()}条边")
        
        return self.g
    
    def add_self_loops(self):
        """为模型添加自环（如果需要）"""
        print("添加自环...")
        
        # 检查是否已有自环
        src, dst = self.g.edges()
        existing_self_loops = set()
        
        for i in range(len(src)):
            if src[i].item() == dst[i].item():
                existing_self_loops.add(src[i].item())
        
        # 找出需要添加自环的节点
        nodes_need_self_loop = []
        for node_id in range(self.g.num_nodes()):
            if node_id not in existing_self_loops:
                nodes_need_self_loop.append(node_id)
        
        if nodes_need_self_loop:
            # 准备自环边数据
            self_loop_src = torch.tensor(nodes_need_self_loop, dtype=torch.long)
            self_loop_dst = torch.tensor(nodes_need_self_loop, dtype=torch.long)
            # 自环关系ID = 原关系总数 * 2 (考虑反向边)
            num_rel_types = len(self.rel2id)
            if hasattr(self, '_has_reverse_edges') and self._has_reverse_edges:
                self_loop_rel_id = num_rel_types * 2
            else:
                self_loop_rel_id = num_rel_types
            
            self_loop_edge_types = torch.full((len(nodes_need_self_loop),), 
                                            self_loop_rel_id, dtype=torch.long)
            
            # 合并原有边和自环边
            all_src = torch.cat([src, self_loop_src])
            all_dst = torch.cat([dst, self_loop_dst])
            all_edge_types = torch.cat([self.g.edata['etype'], self_loop_edge_types])
            
            # 重新创建图
            self.g = dgl.graph((all_src, all_dst), num_nodes=self.g.num_nodes())
            self.g.edata['etype'] = all_edge_types
            self.g.ndata['feat'] = torch.randn(self.g.num_nodes(), 64)
            
            print(f"添加了 {len(nodes_need_self_loop)} 个自环")
        else:
            print("所有节点都已有自环")
        
        print(f"添加自环后: {self.g.num_nodes()}个节点, {self.g.num_edges()}条边")
        
        return self.g
    
    def get_graph_for_model(self, model_name="base", add_reverse=False, add_self_loop=False):
        """根据模型需求获取相应的图结构
        
        Args:
            model_name: 模型名称
            add_reverse: 是否添加反向边
            add_self_loop: 是否添加自环
            
        Returns:
            配置好的DGL图
        """
        print(f"\n为模型 {model_name} 准备图结构...")
        print(f"  - 添加反向边: {add_reverse}")
        print(f"  - 添加自环: {add_self_loop}")
        
        # 重新构建基础图
        self.build_graph()
        
        # 根据需求添加边
        if add_reverse:
            self._has_reverse_edges = True
            self.add_reverse_edges()
        else:
            self._has_reverse_edges = False
            
        if add_self_loop:
            self.add_self_loops()
        
        return self.g
    
    def get_num_relations(self, include_reverse=False, include_self_loop=False):
        """获取关系总数
        
        Args:
            include_reverse: 是否包含反向关系
            include_self_loop: 是否包含自环关系
            
        Returns:
            关系总数
        """
        base_num = len(self.rel2id)
        
        if include_reverse:
            base_num *= 2
            
        if include_self_loop:
            base_num += 1
            
        return base_num
    
    def get_region_entities(self):
        """获取所有区域实体的ID"""
        region_ids = []
        for ent_name, ent_id in self.ent2id.items():
            if "Region" in ent_name or "region" in ent_name.lower():
                region_ids.append(ent_id)
        return region_ids
    
    def get_entity_neighbors(self, entity_id, hop=1):
        """获取实体的邻居节点"""
        neighbors = set()
        current_nodes = {entity_id}
        
        for _ in range(hop):
            next_nodes = set()
            for node in current_nodes:
                # 获取出边邻居
                if self.g.out_degrees(node) > 0:
                    out_edges = self.g.out_edges(node)
                    next_nodes.update(out_edges[1].tolist())
                # 获取入边邻居
                if self.g.in_degrees(node) > 0:
                    in_edges = self.g.in_edges(node)
                    next_nodes.update(in_edges[0].tolist())
            
            neighbors.update(next_nodes)
            current_nodes = next_nodes
        
        return list(neighbors)


def create_sample_data_files(dataset_path="./data/shenyang/"):
    """创建示例数据文件"""
    os.makedirs(dataset_path, exist_ok=True)
    
    # 创建示例知识图谱文件
    kg_file = os.path.join(dataset_path, "kg_without_building.txt")
    if not os.path.exists(kg_file):
        print(f"创建示例知识图谱文件: {kg_file}")
        with open(kg_file, 'w', encoding='utf-8') as f:
            f.write("# 示例知识图谱数据\n")
            f.write("Region_0\tadjacent_to\tRegion_1\n")
            f.write("Region_1\tadjacent_to\tRegion_2\n")
            f.write("POI_0\tlocated_in\tRegion_0\n")
            f.write("POI_1\tlocated_in\tRegion_1\n")
            f.write("Region_0\tpart_of\tDistrict_Heping\n")
            f.write("District_Heping\tpart_of\tCity_Shenyang\n")
    
    # 创建示例区域信息文件
    region_info_file = os.path.join(dataset_path, "shenyang_region2allinfo.json")
    if not os.path.exists(region_info_file):
        print(f"创建示例区域信息文件: {region_info_file}")
        import json
        
        sample_data = {}
        for i in range(50):
            region_name = f"Region_{i}"
            sample_data[region_name] = {
                "energy_consumption": np.random.uniform(100, 1000),
                "population": np.random.randint(1000, 10000),
                "area": np.random.uniform(0.5, 5.0),
                "building_count": np.random.randint(50, 500)
            }
        
        with open(region_info_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    # 测试数据加载
    create_sample_data_files()
    
    # 测试知识图谱加载
    data_loader = Data_CompGCN("./data/shenyang/kg_without_building.txt", "shenyang")
    
    print("\n=== 测试不同模型的图构建 ===")
    
    # 测试1: 基础图（无反向边，无自环）
    base_graph = data_loader.get_graph_for_model("BaseGNN", add_reverse=False, add_self_loop=False)
    print(f"基础图: {base_graph.num_nodes()}节点, {base_graph.num_edges()}边")
    
    # 测试2: RGCN可能需要的图（有反向边，有自环）
    rgcn_graph = data_loader.get_graph_for_model("RGCN", add_reverse=True, add_self_loop=True)
    print(f"RGCN图: {rgcn_graph.num_nodes()}节点, {rgcn_graph.num_edges()}边")
    
    # 测试3: GraphSAGE可能需要的图（无反向边，有自环）
    sage_graph = data_loader.get_graph_for_model("GraphSAGE", add_reverse=False, add_self_loop=True)
    print(f"GraphSAGE图: {sage_graph.num_nodes()}节点, {sage_graph.num_edges()}边")
    
    print(f"\n区域实体数量: {len(data_loader.get_region_entities())}")
    print(f"基础关系数: {len(data_loader.rel2id)}")
    print(f"含反向和自环的关系数: {data_loader.get_num_relations(include_reverse=True, include_self_loop=True)}")