# 优化后的知识图谱三元组示例

## A. 层次归属关系（3种）- 构建空间嵌套层次

### 1. belongsToLand - 建筑物归属地块
```
Building_1001    belongsToLand    Land_201
Building_1002    belongsToLand    Land_201  
Building_1003    belongsToLand    Land_202
```
**解释**：建筑物1001和1002都位于地块201内，建筑物1003位于地块202内。构建建筑物→地块的第一层空间嵌套。

### 2. belongsToRegion - 地块归属街区
```
Land_201    belongsToRegion    Region_101
Land_202    belongsToRegion    Region_101
Land_203    belongsToRegion    Region_102
```
**解释**：地块201和202属于街区101，地块203属于街区102。构建地块→街区的第二层空间嵌套。

### 3. locateAt - POI定位街区
```
POI_5001    locateAt    Region_101
POI_5002    locateAt    Region_101
POI_5003    locateAt    Region_102
```
**解释**：POI 5001和5002位于街区101，POI 5003位于街区102。外部实体接入空间层次。

---

## B. 属性关联关系（6种）- 丰富实体属性维度

### 4. hasFunction - 建筑物功能属性
```
Building_1001    hasFunction    Func_Residential
Building_1002    hasFunction    Func_Commercial
Building_1003    hasFunction    Func_Office
```
**解释**：建筑物1001是住宅，1002是商业建筑，1003是办公建筑。定义建筑物的主要用途。

### 5. hasMorphology - 地块形态属性
```
Land_201    hasMorphology    Morph_HighRiseMidDensity
Land_202    hasMorphology    Morph_LowRiseLowDensity
Land_203    hasMorphology    Morph_MidRiseHighDensity
```
**解释**：地块201是高层中密度形态，地块202是低层低密度，地块203是中层高密度。基于Spacematrix理论的形态分类。

### 6. hasLandUse - 地块土地利用属性
```
Land_201    hasLandUse    LandUse_Mixed
Land_202    hasLandUse    LandUse_Residential
Land_203    hasLandUse    LandUse_Commercial
```
**解释**：地块201是混合用地，地块202是居住用地，地块203是商业用地。反映规划层面的用地性质。

### 7. hasDominantFunction - 街区主导功能
```
Region_101    hasDominantFunction    RegionFunc_Commercial
Region_102    hasDominantFunction    RegionFunc_Residential
Region_103    hasDominantFunction    RegionFunc_Mixed
```
**解释**：街区101以商业功能为主，街区102以居住功能为主，街区103是混合功能。基于POI分布统计的街区功能。

### 8. cateOf - POI类别归属
```
POI_5001    cateOf    Category_Restaurant
POI_5002    cateOf    Category_Shopping
POI_5003    cateOf    Category_Education
```
**解释**：POI 5001是餐饮类，5002是购物类，5003是教育类。POI的功能分类。

### 9. hasPhysicalAttribute - 建筑物物理属性
```
建筑物物理属性分类体系
分类维度与阈值：

面积分类：Small(小，<200㎡) | Medium(中，200-1000㎡) | Large(大，>1000㎡)
高度分类：Low(低，<18m) | Mid(中，18-54m) | High(高，>54m)
年代分类：Old(老，<1990年) | Mid(中，1990-2010年) | New(新，>2010年)

组合示例：
Building_1001    hasPhysicalAttribute    PhysicalAttr_LargeHighNew    # 大面积+高层+新建筑
Building_1002    hasPhysicalAttribute    PhysicalAttr_SmallLowOld     # 小面积+低层+老建筑  
Building_1003    hasPhysicalAttribute    PhysicalAttr_MediumMidMid    # 中面积+中层+中等年代
优势：

27种组合类别：3×3×3=27种不同的建筑物类型
便于聚类分析：离散特征更适合建筑物相似性计算
语义清晰：每种组合都有明确的建筑特征含义
适合GNN：分类特征比连续数值更适合图神经网络处理
```


## C. 同层次空间关系（6种）- 增强层内连通性

### 10. connectedTo - 建筑物间连接
```
Building_1001    connectedTo    Building_1002
Building_1002    connectedTo    Building_1003
Building_1004    connectedTo    Building_1005
```
**解释**：建筑物间的物理连接或功能关联，如连廊连接、同一开发项目等。增强建筑物层的连通性。

### 11. adjacentTo - 地块邻接关系
```
Land_201    adjacentTo    Land_202
Land_202    adjacentTo    Land_203
Land_203    adjacentTo    Land_204
```
**解释**：地块间的空间邻接关系，共享边界的地块建立连接。反映地块间的空间相邻性。

### 12. borderBy - 街区边界相接
```
Region_101    borderBy    Region_102
Region_102    borderBy    Region_103
Region_101    borderBy    Region_104
```
**解释**：街区间共享边界，建立双向的边界相接关系。构建街区层的基础空间网络。

### 13. nearBy - 街区近距离关系
```
Region_101    nearBy    Region_105
Region_102    nearBy    Region_106
Region_103    nearBy    Region_107
```
**解释**：街区间的近距离关系（如距离<1000米），但不直接相邻。扩展街区间的空间关联。

### 14. similarMorphology - 地块形态相似
```
Land_201    similarMorphology    Land_301
Land_202    similarMorphology    Land_302
Land_203    similarMorphology    Land_303
```
**解释**：具有相同或相似形态类型的地块间建立连接。支持基于形态的空间分析。

### 15. similarFunction - 建筑物功能相似
```
Building_1001    similarFunction    Building_2001
Building_1002    similarFunction    Building_2002
Building_1003    similarFunction    Building_2003
```
**解释**：具有相同功能类型的建筑物间建立连接。支持功能空间的网络分析。

---

## D. 跨层次功能关系（4种）- 实现层次间功能连接

### 16. functionalSimilarity - 街区功能相似性
```
Region_101    functionalSimilarity    Region_201
Region_102    functionalSimilarity    Region_202
Region_103    functionalSimilarity    Region_203
```
**解释**：基于POI分布、建筑功能等综合判断的街区功能相似性。支持功能区识别和对比分析。

### 17. highConvenience - 街区便利性关联
```
Region_101    highConvenience    Region_102
Region_103    highConvenience    Region_104
Region_105    highConvenience    Region_106
```
**解释**：都具有高便利性的街区间建立关联（POI丰富、服务完善）。识别城市服务中心网络。

### 18. functionalComplementarity - 街区功能互补
```
Region_101    functionalComplementarity    Region_102
Region_103    functionalComplementarity    Region_104
Region_105    functionalComplementarity    Region_106
```
**解释**：功能互补的街区间关系，如居住区与商业区、办公区与餐饮区。反映城市功能配置模式。

### 19. densityInfluence - 密度影响关系
```
Region_101    densityInfluence    Region_102
Region_103    densityInfluence    Region_104
Region_105    densityInfluence    Region_106
```
**解释**：高密度街区对周边低密度街区的辐射影响。基于城市密度梯度理论建立的关系。

---

## E. 移动性关系（1种）- 连接动态行为

### 20. flowTransition - 街区间人流移动
```
Region_101    flowTransition    Region_102
Region_102    flowTransition    Region_103
Region_101    flowTransition    Region_104
```
**解释**：基于签到数据、模拟流动、吸引流动等建立的街区间人员流动关系。反映城市动态特征。

---

## F. 服务关系（1种）- 外部服务连接

### 21. provideService - 商圈服务街区
```
BC_1    provideService    Region_101
BC_1    provideService    Region_102
BC_2    provideService    Region_103
```
**解释**：商圈BC_1为街区101和102提供服务，商圈BC_2为街区103提供服务。连接商业服务与居住区域。

---

## 增删改对比说明

### ➕ 新增关系（相比原版）
1. **belongsToRegion** - 替代原来模糊的归属关系，明确地块→街区层次
2. **hasDominantFunction** - 新增街区功能属性，基于POI聚合
3. **hasPhysicalAttribute** - 新增建筑物物理属性关系
4. **similarFunction** - 建筑物层面的功能相似性
5. **functionalComplementarity** - 街区间功能互补关系

### 🔄 重命名关系
1. **belongsToBuilding** → **belongsToLand** - 语义更清晰
2. **belongsToLand** → **belongsToRegion** - 明确层次关系
3. **morphologySimilar** → **similarMorphology** - 统一命名规范

### ❌ 删除关系
1. **belongTo** (POI→商圈) - 简化外部关联，减少复杂度
2. **withinRegion** (建筑物→街区) - 通过层次归属链间接表达
3. **densityInfluences** → **densityInfluence** - 简化命名

### 🎯 优化效果
- **层次性**：三层嵌套结构更加清晰
- **连通性**：每层都有丰富的内部连接
- **语义性**：关系命名更加准确
- **完整性**：覆盖物理、功能、空间、动态等多个维度