#!/usr/bin/env python3
"""
数据清洗脚本 - 处理能耗数据中的异常值

功能：
1. 分析能耗数据分布
2. 识别和处理零值
3. 检测和处理异常值
4. 生成清洗报告
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class EnergyDataCleaner:
    """能耗数据清洗器"""
    
    def __init__(self, data_path='./data/shenyang/shenyang_region2allinfo.json'):
        self.data_path = data_path
        self.original_data = None
        self.cleaned_data = None
        self.cleaning_report = {}
        
    def load_data(self):
        """加载原始数据"""
        print("📂 加载原始数据...")
        with open(self.data_path, 'r', encoding='utf-8') as f:
            self.original_data = json.load(f)
        print(f"✅ 加载了 {len(self.original_data)} 个区域的数据")
        
    def analyze_energy_distribution(self):
        """分析能耗分布"""
        print("\n📊 分析能耗分布...")
        
        # 提取能耗值
        energy_values = []
        zero_count = 0
        missing_count = 0
        
        for region_id, data in self.original_data.items():
            if isinstance(data, dict) and 'energy' in data:
                energy = data['energy']
                if energy is None:
                    missing_count += 1
                else:
                    try:
                        energy_val = float(energy)
                        energy_values.append(energy_val)
                        if energy_val == 0:
                            zero_count += 1
                    except:
                        missing_count += 1
            else:
                missing_count += 1
        
        energy_array = np.array(energy_values)
        
        # 统计分析
        stats_info = {
            'count': len(energy_values),
            'zero_count': zero_count,
            'zero_percentage': zero_count / len(energy_values) * 100 if energy_values else 0,
            'missing_count': missing_count,
            'mean': np.mean(energy_array) if len(energy_array) > 0 else 0,
            'std': np.std(energy_array) if len(energy_array) > 0 else 0,
            'min': np.min(energy_array) if len(energy_array) > 0 else 0,
            'max': np.max(energy_array) if len(energy_array) > 0 else 0,
            'q1': np.percentile(energy_array, 25) if len(energy_array) > 0 else 0,
            'median': np.median(energy_array) if len(energy_array) > 0 else 0,
            'q3': np.percentile(energy_array, 75) if len(energy_array) > 0 else 0
        }
        
        print(f"\n📈 能耗统计信息:")
        print(f"  - 有效数据: {stats_info['count']} 个")
        print(f"  - 零值: {stats_info['zero_count']} 个 ({stats_info['zero_percentage']:.1f}%)")
        print(f"  - 缺失值: {stats_info['missing_count']} 个")
        print(f"  - 均值: {stats_info['mean']:.2f}")
        print(f"  - 标准差: {stats_info['std']:.2f}")
        print(f"  - 范围: [{stats_info['min']:.2f}, {stats_info['max']:.2f}]")
        print(f"  - 四分位数: Q1={stats_info['q1']:.2f}, Q2={stats_info['median']:.2f}, Q3={stats_info['q3']:.2f}")
        
        self.cleaning_report['original_stats'] = stats_info
        self.energy_values = energy_array
        
        return stats_info
    
    def detect_outliers(self, method='iqr'):
        """检测异常值"""
        print(f"\n🔍 使用 {method} 方法检测异常值...")
        
        outliers = []
        outlier_indices = []
        
        if method == 'iqr':
            # IQR方法
            Q1 = np.percentile(self.energy_values, 25)
            Q3 = np.percentile(self.energy_values, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            for i, val in enumerate(self.energy_values):
                if val < lower_bound or val > upper_bound:
                    outliers.append(val)
                    outlier_indices.append(i)
            
            print(f"  - IQR范围: [{lower_bound:.2f}, {upper_bound:.2f}]")
            
        elif method == 'zscore':
            # Z-score方法
            z_scores = np.abs(stats.zscore(self.energy_values))
            threshold = 3
            
            for i, (val, z) in enumerate(zip(self.energy_values, z_scores)):
                if z > threshold:
                    outliers.append(val)
                    outlier_indices.append(i)
            
            print(f"  - Z-score阈值: {threshold}")
        
        print(f"  - 检测到异常值: {len(outliers)} 个 ({len(outliers)/len(self.energy_values)*100:.1f}%)")
        
        self.cleaning_report['outliers'] = {
            'method': method,
            'count': len(outliers),
            'percentage': len(outliers)/len(self.energy_values)*100,
            'values': outliers
        }
        
        return outliers, outlier_indices
    
    def visualize_distribution(self, save_path='./energy_distribution_analysis.png'):
        """可视化能耗分布"""
        print("\n📊 生成分布可视化...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Energy Consumption Distribution Analysis', fontsize=16)
        
        # 1. 直方图
        ax1 = axes[0, 0]
        ax1.hist(self.energy_values, bins=50, edgecolor='black', alpha=0.7)
        ax1.axvline(np.mean(self.energy_values), color='red', linestyle='--', label=f'Mean: {np.mean(self.energy_values):.2f}')
        ax1.axvline(np.median(self.energy_values), color='green', linestyle='--', label=f'Median: {np.median(self.energy_values):.2f}')
        ax1.set_title('Energy Distribution Histogram')
        ax1.set_xlabel('Energy Consumption')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        
        # 2. 箱线图
        ax2 = axes[0, 1]
        box_plot = ax2.boxplot(self.energy_values, vert=True, patch_artist=True)
        box_plot['boxes'][0].set_facecolor('lightblue')
        ax2.set_title('Energy Distribution Boxplot')
        ax2.set_ylabel('Energy Consumption')
        
        # 3. QQ图
        ax3 = axes[1, 0]
        stats.probplot(self.energy_values, dist="norm", plot=ax3)
        ax3.set_title('Q-Q Plot')
        
        # 4. 零值和非零值分布
        ax4 = axes[1, 1]
        zero_count = np.sum(self.energy_values == 0)
        non_zero_count = len(self.energy_values) - zero_count
        ax4.pie([non_zero_count, zero_count], 
                labels=[f'Non-zero ({non_zero_count})', f'Zero ({zero_count})'],
                autopct='%1.1f%%',
                colors=['lightgreen', 'lightcoral'])
        ax4.set_title('Zero vs Non-zero Values')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 分布图已保存到: {save_path}")
    
    def clean_data(self, strategy='remove_zeros', outlier_method='iqr'):
        """清洗数据
        
        Args:
            strategy: 清洗策略
                - 'remove_zeros': 移除零值
                - 'remove_outliers': 移除异常值
                - 'remove_both': 移除零值和异常值
                - 'impute_zeros': 用中位数填充零值
                - 'log_transform': 对数变换（保留零值）
        """
        print(f"\n🧹 使用策略 '{strategy}' 清洗数据...")
        
        self.cleaned_data = self.original_data.copy()
        removed_regions = []
        modified_regions = []
        
        # 检测异常值
        outliers, outlier_indices = self.detect_outliers(outlier_method)
        
        # 创建区域ID到索引的映射
        region_ids = []
        energy_list = []
        for region_id, data in self.original_data.items():
            if isinstance(data, dict) and 'energy' in data:
                try:
                    energy = float(data['energy'])
                    region_ids.append(region_id)
                    energy_list.append(energy)
                except:
                    pass
        
        # 应用清洗策略
        if strategy == 'remove_zeros':
            # 移除零值
            for i, (region_id, energy) in enumerate(zip(region_ids, energy_list)):
                if energy == 0:
                    removed_regions.append(region_id)
                    del self.cleaned_data[region_id]
        
        elif strategy == 'remove_outliers':
            # 移除异常值
            for i in outlier_indices:
                if i < len(region_ids):
                    region_id = region_ids[i]
                    removed_regions.append(region_id)
                    del self.cleaned_data[region_id]
        
        elif strategy == 'remove_both':
            # 移除零值和异常值
            for i, (region_id, energy) in enumerate(zip(region_ids, energy_list)):
                if energy == 0 or i in outlier_indices:
                    removed_regions.append(region_id)
                    del self.cleaned_data[region_id]
        
        elif strategy == 'impute_zeros':
            # 用中位数填充零值
            non_zero_values = [e for e in energy_list if e > 0]
            median_value = np.median(non_zero_values) if non_zero_values else 0
            
            for region_id, data in self.cleaned_data.items():
                if isinstance(data, dict) and 'energy' in data:
                    if float(data['energy']) == 0:
                        data['energy'] = median_value
                        modified_regions.append(region_id)
        
        elif strategy == 'log_transform':
            # 对数变换（加1避免log(0)）
            for region_id, data in self.cleaned_data.items():
                if isinstance(data, dict) and 'energy' in data:
                    try:
                        energy = float(data['energy'])
                        data['energy_original'] = energy
                        data['energy'] = np.log1p(energy)
                        modified_regions.append(region_id)
                    except:
                        pass
        
        # 生成清洗报告
        self.cleaning_report['cleaning'] = {
            'strategy': strategy,
            'removed_count': len(removed_regions),
            'modified_count': len(modified_regions),
            'remaining_count': len(self.cleaned_data),
            'removed_percentage': len(removed_regions) / len(self.original_data) * 100
        }
        
        print(f"\n✅ 数据清洗完成:")
        print(f"  - 原始数据: {len(self.original_data)} 个区域")
        print(f"  - 移除: {len(removed_regions)} 个区域")
        print(f"  - 修改: {len(modified_regions)} 个区域")
        print(f"  - 剩余: {len(self.cleaned_data)} 个区域")
        
        return self.cleaned_data
    
    def save_cleaned_data(self, output_path=None):
        """保存清洗后的数据"""
        if output_path is None:
            # 根据清洗策略生成文件名
            strategy = self.cleaning_report.get('cleaning', {}).get('strategy', 'unknown')
            output_path = f'./data/shenyang/shenyang_region2allinfo_cleaned_{strategy}.json'
        
        print(f"\n💾 保存清洗后的数据...")
        
        # 确保目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.cleaned_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 清洗后的数据已保存到: {output_path}")
        
        # 保存清洗报告
        report_path = output_path.replace('.json', '_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.cleaning_report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 清洗报告已保存到: {report_path}")
        
        return output_path
    
    def compare_strategies(self):
        """比较不同清洗策略的效果"""
        print("\n🔬 比较不同清洗策略...")
        
        strategies = ['remove_zeros', 'remove_outliers', 'remove_both', 'impute_zeros']
        results = {}
        
        for strategy in strategies:
            # 重新加载原始数据
            self.load_data()
            self.analyze_energy_distribution()
            
            # 应用清洗策略
            self.clean_data(strategy=strategy)
            
            # 分析清洗后的数据
            cleaned_energy_values = []
            for region_id, data in self.cleaned_data.items():
                if isinstance(data, dict) and 'energy' in data:
                    try:
                        energy = float(data['energy'])
                        cleaned_energy_values.append(energy)
                    except:
                        pass
            
            if cleaned_energy_values:
                results[strategy] = {
                    'count': len(cleaned_energy_values),
                    'mean': np.mean(cleaned_energy_values),
                    'std': np.std(cleaned_energy_values),
                    'min': np.min(cleaned_energy_values),
                    'max': np.max(cleaned_energy_values),
                    'removed_percentage': self.cleaning_report['cleaning']['removed_percentage']
                }
        
        # 生成比较表格
        print("\n📊 清洗策略比较:")
        print("-" * 80)
        print(f"{'策略':<20} {'剩余数据':<10} {'平均值':<10} {'标准差':<10} {'移除比例':<10}")
        print("-" * 80)
        
        for strategy, stats in results.items():
            print(f"{strategy:<20} {stats['count']:<10} "
                  f"{stats['mean']:<10.2f} {stats['std']:<10.2f} "
                  f"{stats['removed_percentage']:<10.1f}%")
        
        return results


def main():
    """主函数"""
    print("=" * 80)
    print("🧹 能耗数据清洗和分析")
    print("=" * 80)
    
    cleaner = EnergyDataCleaner()
    
    # 1. 加载和分析原始数据
    cleaner.load_data()
    cleaner.analyze_energy_distribution()
    
    # 2. 可视化分布
    cleaner.visualize_distribution()
    
    # 3. 比较不同清洗策略
    comparison_results = cleaner.compare_strategies()
    
    # 4. 推荐策略
    print("\n💡 推荐的清洗策略:")
    print("-" * 80)
    
    # 根据零值比例决定策略
    zero_percentage = cleaner.cleaning_report['original_stats']['zero_percentage']
    
    if zero_percentage > 20:
        print(f"⚠️  零值比例较高 ({zero_percentage:.1f}%)，建议:")
        print("  1. 使用 'impute_zeros' 策略（用中位数填充）保留更多数据")
        print("  2. 或使用 'remove_zeros' 策略确保数据质量")
        recommended_strategy = 'impute_zeros'
    elif zero_percentage > 10:
        print(f"⚠️  存在一定比例的零值 ({zero_percentage:.1f}%)，建议:")
        print("  1. 使用 'remove_zeros' 策略移除零值")
        print("  2. 配合移除异常值使用 'remove_both' 策略")
        recommended_strategy = 'remove_zeros'
    else:
        print(f"✅ 零值比例较低 ({zero_percentage:.1f}%)，建议:")
        print("  1. 使用 'remove_outliers' 策略仅移除异常值")
        print("  2. 或保持原始数据")
        recommended_strategy = 'remove_outliers'
    
    # 5. 应用推荐策略并保存
    print(f"\n🚀 应用推荐策略: {recommended_strategy}")
    cleaner.load_data()  # 重新加载原始数据
    cleaner.analyze_energy_distribution()
    cleaner.clean_data(strategy=recommended_strategy)
    output_path = cleaner.save_cleaned_data()
    
    print("\n" + "=" * 80)
    print("✅ 数据清洗完成！")
    print("\n下一步操作:")
    print(f"1. 在config.py中更新region_info_path为: {output_path}")
    print("2. 重新运行模型训练: python main.py --compare_all")
    print("3. 对比清洗前后的模型性能")
    print("=" * 80)


if __name__ == "__main__":
    main()