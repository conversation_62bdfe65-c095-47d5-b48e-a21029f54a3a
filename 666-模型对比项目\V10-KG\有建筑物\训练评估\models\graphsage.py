import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl.nn as dglnn

class GraphSAGE(nn.Module):
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(GraphSAGE, self).__init__()
        self.g = kwargs['g']
        # 初始化节点嵌入
        self.n_embds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        
        # GraphSAGE层（使用DGL的SAGEConv）
        self.layers = nn.ModuleList()
        self.layers.append(dglnn.SAGEConv(node_emb.shape[1], layer_sizes[0], 'mean'))
        for i in range(len(layer_sizes)-1):
            self.layers.append(dglnn.SAGEConv(layer_sizes[i], layer_sizes[i+1], 'mean'))
            
        # dropout层
        self.dropouts = nn.ModuleList()
        for drop_rate in dropout_list:
            self.dropouts.append(nn.Dropout(drop_rate))
            
        # 预测器
        self.projector = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], layer_sizes[-1])
        )
        
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        h = self.n_embds.weight
        
        # 通过GraphSAGE层
        for i, (layer, dropout) in enumerate(zip(self.layers, self.dropouts)):
            h = layer(self.g, h)
            h = F.relu(h)
            h = dropout(h)
        
        # 获取特定节点的嵌入
        h_node = h[node_idx]
        
        # 特征投影
        h_proj = self.projector(h_node)
        
        # 简单的自监督任务：预测自身
        return F.mse_loss(h_proj, h_node.detach())
    
    def get_feature(self, node_idx):
        """提取节点特征（用于下游任务）"""
        with torch.no_grad():
            h = self.n_embds.weight
            
            # 通过GraphSAGE层
            for i, (layer, dropout) in enumerate(zip(self.layers, self.dropouts)):
                h = layer(self.g, h)
                h = F.relu(h)
                h = dropout(h)
            
            # 提取指定节点的特征
            if node_idx is not None:
                return h[node_idx]
            else:
                return h 