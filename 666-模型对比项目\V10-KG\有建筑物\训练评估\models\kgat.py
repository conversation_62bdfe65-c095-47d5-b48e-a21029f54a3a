import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl.function as fn
import dgl

class KGATLayer(nn.Module):
    """KGAT层实现"""
    def __init__(self, in_dim, out_dim, num_rels, dropout=0.0):
        super(K<PERSON>TLayer, self).__init__()
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.num_rels = num_rels
        self.dropout = nn.Dropout(dropout)
        
        # 实体转换
        self.W_ent = nn.Linear(in_dim, out_dim)
        
        # 关系转换
        self.W_rel = nn.Linear(in_dim, out_dim)
        
        # 注意力相关参数 - 修复尺寸问题
        self.a_src = nn.Parameter(torch.Tensor(out_dim, 1))
        self.a_rel = nn.Parameter(torch.Tensor(out_dim, 1))
        self.a_dst = nn.Parameter(torch.Tensor(out_dim, 1))
        # 添加回bias参数
        self.bias = nn.Parameter(torch.Tensor(out_dim))
        
        # 初始化参数
        nn.init.xavier_uniform_(self.a_src)
        nn.init.xavier_uniform_(self.a_rel)
        nn.init.xavier_uniform_(self.a_dst)
        nn.init.zeros_(self.bias)  # 初始化偏置为零
        
    def edge_attention(self, edges):
        """计算边的注意力分数"""
        # 基于源节点、关系和目标节点计算注意力
        h_src = edges.src['h']  # 源节点特征
        h_rel = edges.data['h_rel']  # 关系特征
        h_dst = edges.dst['h']  # 目标节点特征
        
        # 分别计算三部分的注意力得分
        a_src = torch.matmul(h_src, self.a_src)
        a_rel = torch.matmul(h_rel, self.a_rel)
        a_dst = torch.matmul(h_dst, self.a_dst)
        
        # 合并注意力得分
        attention = a_src + a_rel + a_dst
        attention = F.leaky_relu(attention)
        
        return {'a': attention}
        
    def message_func(self, edges):
        """定义消息函数"""
        # 返回关系特定的源节点特征和注意力分数
        return {
            'h': edges.src['h'],
            'a': edges.data['a'],
            'h_rel': edges.data['h_rel']
        }
        
    def reduce_func(self, nodes):
        """定义聚合函数"""
        # 基于注意力权重聚合邻居的消息
        alpha = F.softmax(nodes.mailbox['a'], dim=1)
        h = torch.sum(alpha * nodes.mailbox['h'], dim=1)
        return {'h': h}
        
    def forward(self, g, h_ent, h_rel):
        """前向传播"""
        with g.local_scope():
            # 节点和关系特征转换
            h_ent_new = self.W_ent(h_ent)
            h_rel_new = self.W_rel(h_rel)
            
            g.ndata['h'] = h_ent_new
            
            # 为每种关系类型计算注意力和消息传递
            etype = g.edata['etype'].long()
            
            # 将关系特征分配给边
            g.edata['h_rel'] = h_rel_new[etype]
            
            # 注意力计算
            g.apply_edges(self.edge_attention)
            
            # 消息传递
            g.update_all(self.message_func, self.reduce_func)
            
            # 获取更新后的节点表示
            h_out = g.ndata['h']
            
            # 添加非线性和dropout
            h_out = F.relu(h_out + self.bias)
            h_out = self.dropout(h_out)
            
            return h_out, h_rel_new
            
    def att_proj(self, z):
        """投影到注意力空间"""
        return torch.matmul(z, self.a)

class KGAT(nn.Module):
    """知识图谱注意力网络"""
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(KGAT, self).__init__()
        self.g = kwargs['g']
        self.num_nodes = node_emb.shape[0]
        self.node_dim = node_emb.shape[1]
        self.num_rels = rel_emb.shape[0]
        
        # 初始节点和关系嵌入
        self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        self.rel_embeds = nn.Embedding.from_pretrained(rel_emb, freeze=True)
        
        # KGAT层
        self.layers = nn.ModuleList()
        layer_dims = [self.node_dim] + layer_sizes
        for i in range(len(layer_sizes)):
            self.layers.append(KGATLayer(
                layer_dims[i], 
                layer_dims[i+1], 
                self.num_rels, 
                dropout=dropout_list[i]
            ))
        
        # 预测器（用于特征提取或下游任务）
        self.projector = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], layer_sizes[-1])
        )
    
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        h_ent = self.node_embeds.weight
        h_rel = self.rel_embeds.weight
        
        # 通过KGAT层
        for layer in self.layers:
            h_ent, h_rel = layer(self.g, h_ent, h_rel)
        
        # 获取特定节点的嵌入
        h_node = h_ent[node_idx]
        
        # 特征投影
        h_proj = self.projector(h_node)
        
        # 简单的自监督任务：预测自身
        return F.mse_loss(h_proj, h_node.detach())
    
    def get_feature(self, node_idx):
        """提取节点特征（用于下游任务）"""
        with torch.no_grad():
            h_ent = self.node_embeds.weight
            h_rel = self.rel_embeds.weight
            
            # 通过KGAT层
            for layer in self.layers:
                h_ent, h_rel = layer(self.g, h_ent, h_rel)
            
            # 提取指定节点的特征
            if node_idx is not None:
                return h_ent[node_idx]
            else:
                return h_ent 