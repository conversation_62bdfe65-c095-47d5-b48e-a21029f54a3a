import os
import json
import pandas as pd
from pathlib import Path

def load_json_files(folder_path):
    """Load all JSON files from a given folder and extract metrics."""
    metrics_dict = {}
    for file_name in os.listdir(folder_path):
        if file_name.endswith('.json'):
            file_path = os.path.join(folder_path, file_name)
            with open(file_path, 'r') as f:
                data = json.load(f)
                model_name = file_name.replace('_energy_results.json', '')
                metrics_dict[model_name] = data['metrics']
    return metrics_dict

def compare_metrics(folder1_path, folder2_path):
    """Compare metrics from JSON files in two folders and display results."""
    # Load metrics from both subfolders
    metrics1 = load_json_files(folder1_path)
    metrics2 = load_json_files(folder2_path)
    
    # Get all unique model names
    all_models = set(metrics1.keys()).union(set(metrics2.keys()))
    
    # Prepare data for comparison
    comparison_data = []
    for model in sorted(all_models):
        row = {'Model': model}
        
        # Metrics from subfolder1
        if model in metrics1:
            row['R2 (Folder 1)'] = metrics1[model].get('r2', 'N/A')
            row['RMSE (Folder 1)'] = metrics1[model].get('rmse', 'N/A')
            row['MAE (Folder 1)'] = metrics1[model].get('mae', 'N/A')
            row['MAPE (Folder 1)'] = metrics1[model].get('mape', 'N/A')
        else:
            row['R2 (Folder 1)'] = 'N/A'
            row['RMSE (Folder 1)'] = 'N/A'
            row['MAE (Folder 1)'] = 'N/A'
            row['MAPE (Folder 1)'] = 'N/A'
        
        # Metrics from subfolder2
        if model in metrics2:
            row['R2 (Folder 2)'] = metrics2[model].get('r2', 'N/A')
            row['RMSE (Folder 2)'] = metrics2[model].get('rmse', 'N/A')
            row['MAE (Folder 2)'] = metrics2[model].get('mae', 'N/A')
            row['MAPE (Folder 2)'] = metrics2[model].get('mape', 'N/A')
        else:
            row['R2 (Folder 2)'] = 'N/A'
            row['RMSE (Folder 2)'] = 'N/A'
            row['MAE (Folder 2)'] = 'N/A'
            row['MAPE (Folder 2)'] = 'N/A'
        
        # Calculate differences if metrics exist in both
        if model in metrics1 and model in metrics2:
            row['R2 Diff (F2 - F1)'] = (
                metrics2[model].get('r2', 0) - metrics1[model].get('r2', 0)
                if isinstance(metrics1[model].get('r2'), (int, float)) and
                isinstance(metrics2[model].get('r2'), (int, float))
                else 'N/A'
            )
            row['RMSE Diff (F2 - F1)'] = (
                metrics2[model].get('rmse', 0) - metrics1[model].get('rmse', 0)
                if isinstance(metrics1[model].get('rmse'), (int, float)) and
                isinstance(metrics2[model].get('rmse'), (int, float))
                else 'N/A'
            )
            row['MAE Diff (F2 - F1)'] = (
                metrics2[model].get('mae', 0) - metrics1[model].get('mae', 0)
                if isinstance(metrics1[model].get('mae'), (int, float)) and
                isinstance(metrics2[model].get('mae'), (int, float))
                else 'N/A'
            )
            row['MAPE Diff (F2 - F1)'] = (
                metrics2[model].get('mape', 0) - metrics1[model].get('mape', 0)
                if isinstance(metrics1[model].get('mape'), (int, float)) and
                isinstance(metrics2[model].get('mape'), (int, float))
                else 'N/A'
            )
        else:
            row['R2 Diff (F2 - F1)'] = 'N/A'
            row['RMSE Diff (F2 - F1)'] = 'N/A'
            row['MAE Diff (F2 - F1)'] = 'N/A'
            row['MAPE Diff (F2 - F1)'] = 'N/A'
        
        comparison_data.append(row)
    
    # Create DataFrame for better visualization
    df = pd.DataFrame(comparison_data)
    df = df[['Model', 
             'R2 (Folder 1)', 'R2 (Folder 2)', 'R2 Diff (F2 - F1)',
             'RMSE (Folder 1)', 'RMSE (Folder 2)', 'RMSE Diff (F2 - F1)',
             'MAE (Folder 1)', 'MAE (Folder 2)', 'MAE Diff (F2 - F1)',
             'MAPE (Folder 1)', 'MAPE (Folder 2)', 'MAPE Diff (F2 - F1)']]
    
    # Print the comparison table
    print("\nComparison of Model Metrics Between Subfolder1 and Subfolder2:")
    print(df.to_string(index=False))
    
    return df

if __name__ == "__main__":
    # Define paths to the subfolders
    base_dir = r"D:\研二\能耗估算\666-模型对比项目\V7\saved_models"  # Replace with your actual folder path
    subfolder1_path = Path(base_dir) / "subfolder1"
    subfolder2_path = Path(base_dir) / "subfolder2"
    
    # Ensure folders exist
    if not subfolder1_path.exists() or not subfolder2_path.exists():
        print("Error: One or both subfolders do not exist. Please check the paths.")
    else:
        compare_metrics(subfolder1_path, subfolder2_path)