import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

class Identity(nn.Module):
    def __init__(self):
        super(Identity, self).__init__()
        
    def forward(self, x):
        return x

class ViTModel(nn.Module):
    def __init__(self, **kwargs):
        super(ViTModel, self).__init__()
        # 加载预训练ViT
        self.encoder = models.vit_b_16(pretrained=True)
        self.encoder.heads = Identity()
        
        # 特征投影层
        self.projector = nn.Sequential(
            nn.Linear(768, 512, bias=False),
            nn.ReLU(),
            nn.Linear(512, 64, bias=False)
        )
        
    def forward(self, images, node_idx=None):
        """前向传播用于训练"""
        # 确保输入是批次形式 (B,C,H,W)
        if images.dim() == 3:  # 单张图像(C,H,W)
            images = images.unsqueeze(0)  # 添加批次维度
        
        # 通过ViT提取特征
        features = self.encoder(images)
        
        # 投影特征
        proj_features = self.projector(features)
        
        # 简单的自监督任务：使用对比损失
        loss = self._contrastive_loss(proj_features)
        return loss
    
    def _contrastive_loss(self, features):
        """简单的对比损失实现"""
        batch_size = features.shape[0]
        
        # 计算特征之间的相似度矩阵
        sim_matrix = torch.mm(features, features.t())
        
        # 对角线上是正样本
        labels = torch.arange(batch_size).to(features.device)
        
        # InfoNCE损失
        loss = F.cross_entropy(sim_matrix / 0.1, labels)
        return loss
    
    def get_feature(self, images):
        """提取图像特征（用于下游任务）"""
        with torch.no_grad():
            # 确保输入是批次形式
            if images.dim() == 3:
                images = images.unsqueeze(0)
            
            # 通过ViT提取特征
            features = self.encoder(images)
            return features 