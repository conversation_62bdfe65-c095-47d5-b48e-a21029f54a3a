import torch
import torch.nn as nn
import torch.nn.functional as F
import dgl.nn as dglnn
import dgl

class GATLayer(nn.Module):
    """图注意力网络层实现"""
    def __init__(self, in_dim, out_dim, num_heads, dropout=0.0, activation=None, residual=True):
        super(GATLayer, self).__init__()
        self.gat = dglnn.GATConv(
            in_feats=in_dim, 
            out_feats=out_dim, 
            num_heads=num_heads,
            feat_drop=dropout,
            attn_drop=dropout,
            residual=residual,
            activation=activation
        )
        
    def forward(self, g, h):
        h = self.gat(g, h)
        # 多头注意力结果拼接或平均
        h = h.mean(dim=1)  # 平均多头注意力结果
        return h

class GAT(nn.Module):
    """完整的GAT模型"""
    def __init__(self, node_emb, rel_emb, layer_sizes, dropout_list, **kwargs):
        super(GAT, self).__init__()
        self.g = kwargs['g']
        self.num_nodes = node_emb.shape[0]
        self.node_dim = node_emb.shape[1]
        
        # 初始节点嵌入
        self.node_embeds = nn.Embedding.from_pretrained(node_emb, freeze=True)
        
        # GAT层
        self.layers = nn.ModuleList()
        layer_dims = [self.node_dim] + layer_sizes
        num_heads = [8, 8]  # 多头注意力的头数
        
        for i in range(len(layer_sizes)):
            self.layers.append(GATLayer(
                layer_dims[i], 
                layer_dims[i+1],
                num_heads=num_heads[i],
                dropout=dropout_list[i],
                activation=F.elu if i < len(layer_sizes)-1 else None
            ))
        
        # 预测器（用于特征提取或下游任务）
        self.projector = nn.Sequential(
            nn.Linear(layer_sizes[-1], layer_sizes[-1]),
            nn.ReLU(),
            nn.Linear(layer_sizes[-1], layer_sizes[-1])
        )
    
    def forward(self, node_idx):
        """前向传播（用于训练）"""
        h = self.node_embeds.weight
        
        # 通过GAT层
        for layer in self.layers:
            h = layer(self.g, h)
        
        # 获取特定节点的嵌入
        h_node = h[node_idx]
        
        # 特征投影
        h_proj = self.projector(h_node)
        
        # 简单的自监督任务：预测自身
        return F.mse_loss(h_proj, h_node.detach())
    
    def get_feature(self, node_idx):
        """提取节点特征（用于下游任务）"""
        with torch.no_grad():
            h = self.node_embeds.weight
            
            # 通过GAT层
            for layer in self.layers:
                h = layer(self.g, h)
            
            # 提取指定节点的特征
            if node_idx is not None:
                return h[node_idx]
            else:
                return h 